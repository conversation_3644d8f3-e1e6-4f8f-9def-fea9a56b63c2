import { useRef, useEffect, useState } from 'react';
import PDFUploader from './PDFUploader';
import YouTubeInserter from './YouTubeInserter';

const WYSIWYGEditor = ({ value, onChange, placeholder = 'اكتب المحتوى هنا...', height = 400 }) => {
  const editorRef = useRef(null);
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const [showPDFUploader, setShowPDFUploader] = useState(false);
  const [showYouTubeInserter, setShowYouTubeInserter] = useState(false);

  const handleContentChange = () => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML;

      // حساب عدد الكلمات والأحرف
      const text = content.replace(/<[^>]*>/g, ''); // إزالة HTML tags
      const words = text.trim().split(/\s+/).filter(word => word.length > 0);
      setWordCount(words.length);
      setCharCount(text.length);

      // استدعاء onChange مع debouncing
      if (onChange) {
        clearTimeout(window.editorTimeout);
        window.editorTimeout = setTimeout(() => {
          onChange(content);
        }, 300); // انتظار 300ms قبل إرسال التحديث
      }
    }
  };

  const execCommand = (command, value = null) => {
    document.execCommand(command, false, value);
    editorRef.current.focus();
    handleContentChange();
  };

  const insertImage = () => {
    // إنشاء input لاختيار الصورة
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = async (e) => {
      const file = e.target.files[0];
      if (file) {
        await uploadAndInsertImage(file);
      }
    };
    input.click();
  };

  const insertImageFromURL = () => {
    const url = prompt('أدخل رابط الصورة:');
    if (url) {
      const imageHTML = `
        <img
          src="${url}"
          alt="صورة"
          style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin: 10px 0; display: block;"
        />
      `;
      execCommand('insertHTML', imageHTML);
      handleContentChange();
    }
  };

  const uploadAndInsertImage = async (file) => {
    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
      alert('يُسمح فقط بملفات الصور');
      return;
    }

    // التحقق من حجم الملف (5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('حجم الصورة يجب أن يكون أقل من 5 ميجابايت');
      return;
    }

    try {
      // إظهار مؤشر التحميل
      const loadingImg = document.createElement('img');
      loadingImg.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMTgiIHN0cm9rZT0iIzMzMzMzMyIgc3Ryb2tlLXdpZHRoPSI0IiBzdHJva2UtZGFzaGFycmF5PSI1NiIgc3Ryb2tlLWRhc2hvZmZzZXQ9IjU2Ij4KPGFuaW1hdGVUcmFuc2Zvcm0gYXR0cmlidXRlTmFtZT0idHJhbnNmb3JtIiB0eXBlPSJyb3RhdGUiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiB2YWx1ZXM9IjAgMjAgMjA7MzYwIDIwIDIwIi8+CjwvYW5pbWF0ZVRyYW5zZm9ybT4KPC9jaXJjbGU+Cjwvc3ZnPgo=';
      loadingImg.style.width = '40px';
      loadingImg.style.height = '40px';
      loadingImg.style.display = 'block';
      loadingImg.style.margin = '10px auto';

      // إدراج مؤشر التحميل
      execCommand('insertHTML', loadingImg.outerHTML);

      // رفع الصورة
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Upload result:', result);

      if (result.success) {
        // إزالة مؤشر التحميل وإدراج الصورة الحقيقية
        const editor = editorRef.current;
        if (editor) {
          const loadingImgs = editor.querySelectorAll('img[src*="data:image/svg+xml"]');
          if (loadingImgs.length > 0) {
            const lastLoadingImg = loadingImgs[loadingImgs.length - 1];
            const realImg = document.createElement('img');
            realImg.src = result.image.url;
            realImg.alt = result.image.name;
            realImg.style.maxWidth = '100%';
            realImg.style.height = 'auto';
            realImg.style.borderRadius = '8px';
            realImg.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
            realImg.style.margin = '10px 0';
            realImg.style.display = 'block';

            // إضافة event listener للتحقق من تحميل الصورة
            realImg.onload = () => {
              console.log('Image loaded successfully:', result.image.url);
            };

            realImg.onerror = () => {
              console.error('Failed to load image:', result.image.url);
              realImg.style.border = '2px dashed #ff6b6b';
              realImg.style.padding = '20px';
              realImg.style.textAlign = 'center';
              realImg.alt = 'فشل في تحميل الصورة';
            };

            lastLoadingImg.parentNode.replaceChild(realImg, lastLoadingImg);
          }
        }

        handleContentChange();
      } else {
        console.error('Upload failed:', result);
        alert(result.error || 'خطأ في رفع الصورة');
        // إزالة مؤشر التحميل في حالة الخطأ
        const editor = editorRef.current;
        if (editor) {
          const loadingImgs = editor.querySelectorAll('img[src*="data:image/svg+xml"]');
          if (loadingImgs.length > 0) {
            const lastLoadingImg = loadingImgs[loadingImgs.length - 1];
            lastLoadingImg.remove();
          }
        }
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('خطأ في رفع الصورة');
      // إزالة مؤشر التحميل في حالة الخطأ
      const editor = editorRef.current;
      if (editor) {
        const loadingImgs = editor.querySelectorAll('img[src*="data:image/svg+xml"]');
        if (loadingImgs.length > 0) {
          const lastLoadingImg = loadingImgs[loadingImgs.length - 1];
          lastLoadingImg.remove();
        }
      }
    }
  };

  const insertLink = () => {
    const url = prompt('أدخل الرابط:');
    if (url) {
      execCommand('createLink', url);
    }
  };

  const insertYouTubeVideo = () => {
    setShowYouTubeInserter(true);
  };

  const handleYouTubeInsert = (embedHTML) => {
    console.log('YouTube insert called with:', embedHTML);

    // إدراج HTML في المحرر
    execCommand('insertHTML', embedHTML);

    // تحديث المحتوى
    handleContentChange();

    // إجبار إعادة رسم المحرر
    setTimeout(() => {
      if (editorRef.current) {
        const content = editorRef.current.innerHTML;
        editorRef.current.innerHTML = content;
      }
    }, 100);

    console.log('YouTube video inserted, editor content updated');
  };

  const insertTable = () => {
    const rows = prompt('عدد الصفوف:', '3');
    const cols = prompt('عدد الأعمدة:', '3');

    if (rows && cols && !isNaN(rows) && !isNaN(cols)) {
      let tableHTML = '<table style="width: 100%; border-collapse: collapse; margin: 20px 0; direction: rtl;">';

      for (let i = 0; i < parseInt(rows); i++) {
        tableHTML += '<tr>';
        for (let j = 0; j < parseInt(cols); j++) {
          tableHTML += '<td style="border: 1px solid #ddd; padding: 12px; text-align: right;">خلية</td>';
        }
        tableHTML += '</tr>';
      }

      tableHTML += '</table>';
      execCommand('insertHTML', tableHTML);
      handleContentChange();
    }
  };

  const insertQuote = () => {
    const selectedText = window.getSelection().toString();
    const quoteText = selectedText || 'اكتب الاقتباس هنا...';

    const quoteHTML = `
      <blockquote style="border-right: 4px solid #3498db; border-left: none; margin: 20px 0; padding: 20px; background-color: #f8f9fa; font-style: italic; border-radius: 8px; direction: rtl;">
        <p style="margin: 0; font-size: 18px; line-height: 1.6; color: #2c3e50;">"${quoteText}"</p>
      </blockquote>
    `;

    if (selectedText) {
      execCommand('insertHTML', quoteHTML);
    } else {
      execCommand('insertHTML', quoteHTML);
    }
    handleContentChange();
  };

  const insertCodeBlock = () => {
    const code = prompt('أدخل الكود:');
    if (code) {
      const codeHTML = `
        <pre style="background-color: #f4f4f4; border: 1px solid #ddd; border-radius: 8px; padding: 16px; margin: 20px 0; overflow-x: auto; direction: ltr; text-align: left;"><code style="font-family: 'Courier New', monospace; font-size: 14px; color: #333;">${code}</code></pre>
      `;
      execCommand('insertHTML', codeHTML);
      handleContentChange();
    }
  };

  const insertDivider = () => {
    const dividerHTML = `
      <div style="margin: 30px 0; text-align: center;">
        <hr style="border: none; height: 2px; background: linear-gradient(to right, transparent, #3498db, transparent); margin: 0;">
      </div>
    `;
    execCommand('insertHTML', dividerHTML);
    handleContentChange();
  };

  const insertHighlight = () => {
    const selectedText = window.getSelection().toString();
    if (selectedText) {
      const highlightHTML = `<mark style="background-color: #fff3cd; padding: 2px 4px; border-radius: 3px;">${selectedText}</mark>`;
      execCommand('insertHTML', highlightHTML);
      handleContentChange();
    } else {
      alert('يرجى تحديد النص المراد تمييزه أولاً');
    }
  };

  const insertCallout = () => {
    const text = prompt('أدخل نص التنبيه:');
    const type = prompt('نوع التنبيه (info/warning/success/error):', 'info');

    if (text) {
      const colors = {
        info: { bg: '#e3f2fd', border: '#2196f3', icon: 'ℹ️' },
        warning: { bg: '#fff3e0', border: '#ff9800', icon: '⚠️' },
        success: { bg: '#e8f5e8', border: '#4caf50', icon: '✅' },
        error: { bg: '#ffebee', border: '#f44336', icon: '❌' }
      };

      const color = colors[type] || colors.info;

      const calloutHTML = `
        <div style="background-color: ${color.bg}; border-right: 4px solid ${color.border}; border-left: none; padding: 16px; margin: 20px 0; border-radius: 8px; direction: rtl;">
          <div style="display: flex; align-items: flex-start; gap: 12px;">
            <span style="font-size: 20px;">${color.icon}</span>
            <p style="margin: 0; color: #333; line-height: 1.6;">${text}</p>
          </div>
        </div>
      `;
      execCommand('insertHTML', calloutHTML);
      handleContentChange();
    }
  };

  const changeFontSize = (size) => {
    execCommand('fontSize', size);
  };

  const changeFontFamily = (font) => {
    execCommand('fontName', font);
  };

  const changeTextColor = (color) => {
    execCommand('foreColor', color);
  };

  const changeBackgroundColor = (color) => {
    execCommand('backColor', color);
  };

  useEffect(() => {
    if (editorRef.current && value) {
      editorRef.current.innerHTML = value;
      handleContentChange();
    }

    // إضافة مستمعات السحب والإفلات
    const editor = editorRef.current;
    if (editor) {
      const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
        editor.style.backgroundColor = '#f0f9ff';
        editor.style.borderColor = '#3b82f6';
      };

      const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        editor.style.backgroundColor = '';
        editor.style.borderColor = '';
      };

      const handleDrop = async (e) => {
        e.preventDefault();
        e.stopPropagation();
        editor.style.backgroundColor = '';
        editor.style.borderColor = '';

        const files = Array.from(e.dataTransfer.files);
        const imageFiles = files.filter(file => file.type.startsWith('image/'));

        for (const file of imageFiles) {
          await uploadAndInsertImage(file);
        }
      };

      editor.addEventListener('dragover', handleDragOver);
      editor.addEventListener('dragleave', handleDragLeave);
      editor.addEventListener('drop', handleDrop);

      return () => {
        editor.removeEventListener('dragover', handleDragOver);
        editor.removeEventListener('dragleave', handleDragLeave);
        editor.removeEventListener('drop', handleDrop);
        // تنظيف timeout
        if (window.editorTimeout) {
          clearTimeout(window.editorTimeout);
        }
      };
    }
  }, []);

  return (
    <div className="wysiwyg-editor">
      {/* شريط الأدوات */}
      <div className="toolbar">
        {/* التنسيق الأساسي */}
        <div className="toolbar-group">
          <button onClick={() => execCommand('bold')} title="عريض">
            <strong>B</strong>
          </button>
          <button onClick={() => execCommand('italic')} title="مائل">
            <em>I</em>
          </button>
          <button onClick={() => execCommand('underline')} title="تحته خط">
            <u>U</u>
          </button>
          <button onClick={() => execCommand('strikeThrough')} title="مشطوب">
            <s>S</s>
          </button>
          <button onClick={insertHighlight} title="تمييز النص المحدد">
            🖍️
          </button>
        </div>

        <div className="separator"></div>

        {/* الخطوط والأحجام */}
        <div className="toolbar-group">
          <select onChange={(e) => changeFontFamily(e.target.value)} defaultValue="" title="نوع الخط">
            <option value="">الخط</option>
            <option value="Cairo">Cairo</option>
            <option value="Tajawal">Tajawal</option>
            <option value="Amiri">Amiri</option>
            <option value="Arial">Arial</option>
            <option value="Times New Roman">Times New Roman</option>
          </select>

          <select onChange={(e) => changeFontSize(e.target.value)} defaultValue="" title="حجم الخط">
            <option value="">الحجم</option>
            <option value="1">صغير جداً</option>
            <option value="2">صغير</option>
            <option value="3">متوسط</option>
            <option value="4">كبير</option>
            <option value="5">كبير جداً</option>
            <option value="6">ضخم</option>
            <option value="7">ضخم جداً</option>
          </select>
        </div>

        <div className="separator"></div>

        {/* الألوان */}
        <div className="toolbar-group">
          <input
            type="color"
            onChange={(e) => changeTextColor(e.target.value)}
            title="لون النص"
            className="color-picker"
          />
          <input
            type="color"
            onChange={(e) => changeBackgroundColor(e.target.value)}
            title="لون الخلفية"
            className="color-picker"
          />
        </div>

        <div className="separator"></div>

        {/* المحاذاة */}
        <div className="toolbar-group">
          <button onClick={() => execCommand('justifyRight')} title="محاذاة يمين">
            ⇤
          </button>
          <button onClick={() => execCommand('justifyCenter')} title="محاذاة وسط">
            ⇔
          </button>
          <button onClick={() => execCommand('justifyLeft')} title="محاذاة يسار">
            ⇥
          </button>
          <button onClick={() => execCommand('justifyFull')} title="ضبط">
            ↕
          </button>
        </div>

        <div className="separator"></div>

        {/* القوائم */}
        <div className="toolbar-group">
          <button onClick={() => execCommand('insertUnorderedList')} title="قائمة نقطية">
            • القائمة
          </button>
          <button onClick={() => execCommand('insertOrderedList')} title="قائمة مرقمة">
            1. القائمة
          </button>
          <button onClick={() => execCommand('indent')} title="زيادة المسافة البادئة">
            ⇥
          </button>
          <button onClick={() => execCommand('outdent')} title="تقليل المسافة البادئة">
            ⇤
          </button>
        </div>

        <div className="separator"></div>

        {/* العناوين */}
        <div className="toolbar-group">
          <button onClick={() => execCommand('formatBlock', 'h1')} title="عنوان 1">
            H1
          </button>
          <button onClick={() => execCommand('formatBlock', 'h2')} title="عنوان 2">
            H2
          </button>
          <button onClick={() => execCommand('formatBlock', 'h3')} title="عنوان 3">
            H3
          </button>
          <button onClick={() => execCommand('formatBlock', 'p')} title="فقرة">
            P
          </button>
        </div>

        <div className="separator"></div>

        {/* العناصر المتقدمة */}
        <div className="toolbar-group">
          <button onClick={insertQuote} title="اقتباس">
            💬
          </button>
          <button onClick={insertTable} title="جدول">
            📊
          </button>
          <button onClick={insertCodeBlock} title="كتلة كود">
            💻
          </button>
          <button onClick={insertCallout} title="تنبيه">
            💡
          </button>
          <button onClick={insertDivider} title="خط فاصل">
            ➖
          </button>
        </div>

        <div className="separator"></div>

        {/* الوسائط */}
        <div className="toolbar-group">
          <button onClick={insertLink} title="إدراج رابط">
            🔗
          </button>
          <button onClick={insertImage} title="رفع صورة من الجهاز (أو اسحب الصورة إلى المحرر)">
            📷
          </button>
          <button onClick={insertImageFromURL} title="إدراج صورة من رابط">
            🖼️
          </button>
          <button onClick={insertYouTubeVideo} title="إدراج فيديو YouTube">
            📺
          </button>
          <button onClick={() => setShowPDFUploader(!showPDFUploader)} title="إدراج ملف PDF">
            📄
          </button>
        </div>

        <div className="separator"></div>

        {/* التحكم */}
        <div className="toolbar-group">
          <button onClick={() => execCommand('undo')} title="تراجع">
            ↶
          </button>
          <button onClick={() => execCommand('redo')} title="إعادة">
            ↷
          </button>
          <button onClick={() => execCommand('removeFormat')} title="إزالة التنسيق">
            🧹
          </button>
        </div>
      </div>

      {/* رافع ملفات PDF */}
      {showPDFUploader && (
        <div className="pdf-uploader-section">
          <PDFUploader
            onFileUploaded={(file) => {
              console.log('File uploaded:', file);
              setShowPDFUploader(false);
            }}
          />
        </div>
      )}

      {/* مدرج فيديوهات YouTube */}
      {showYouTubeInserter && (
        <div className="youtube-inserter-overlay">
          <div className="youtube-inserter-modal">
            <YouTubeInserter
              onInsert={handleYouTubeInsert}
              onClose={() => setShowYouTubeInserter(false)}
            />
          </div>
        </div>
      )}

      {/* منطقة التحرير */}
      <div
        ref={editorRef}
        contentEditable
        className="editor-content"
        style={{ minHeight: height - 100 }}
        onInput={handleContentChange}
        onKeyUp={handleContentChange}
        data-placeholder={placeholder}
        suppressContentEditableWarning={true}
      />

      {/* شريط الحالة */}
      <div className="status-bar">
        <span>الكلمات: {wordCount}</span>
        <span>الأحرف: {charCount}</span>
      </div>

      <style jsx>{`
        .wysiwyg-editor {
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          background: white;
          direction: rtl;
          font-family: 'Arial', sans-serif;
        }

        .toolbar {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px;
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          border-bottom: 1px solid #e0e0e0;
          border-radius: 12px 12px 0 0;
          flex-wrap: wrap;
          box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .toolbar-group {
          display: flex;
          gap: 4px;
          align-items: center;
          padding: 6px;
          background: rgba(255,255,255,0.8);
          border-radius: 8px;
          border: 1px solid rgba(0,0,0,0.1);
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .toolbar button {
          min-width: 36px;
          height: 36px;
          border: 1px solid #d0d0d0;
          border-radius: 6px;
          background: white;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 13px;
          font-weight: 500;
          transition: all 0.2s ease;
          padding: 0 8px;
          white-space: nowrap;
        }

        .toolbar select {
          height: 36px;
          border: 1px solid #d0d0d0;
          border-radius: 6px;
          background: white;
          cursor: pointer;
          font-size: 13px;
          padding: 0 8px;
          transition: all 0.2s ease;
        }

        .color-picker {
          width: 36px !important;
          height: 36px !important;
          border: 1px solid #d0d0d0 !important;
          border-radius: 6px !important;
          cursor: pointer !important;
          padding: 2px !important;
        }

        .toolbar button:hover,
        .toolbar select:hover {
          background-color: #e9ecef;
          border-color: #adb5bd;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .toolbar button:active {
          background-color: #007bff;
          color: white;
          border-color: #007bff;
          transform: translateY(0);
          box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .color-picker:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .separator {
          width: 1px;
          height: 24px;
          background-color: #d0d0d0;
          margin: 0 4px;
        }

        .editor-content {
          padding: 16px;
          font-size: 16px;
          line-height: 1.6;
          direction: rtl;
          text-align: right;
          outline: none;
          overflow-y: auto;
          min-height: ${height - 100}px;
        }

        /* دعم فيديوهات YouTube في المحرر */
        .editor-content .youtube-video-placeholder {
          display: block !important;
          margin: 20px auto !important;
          max-width: 100% !important;
          border-radius: 12px !important;
          overflow: hidden !important;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
          background: linear-gradient(135deg, #ff0000, #cc0000) !important;
          min-height: 200px !important;
          color: white !important;
          font-family: Arial, sans-serif !important;
          cursor: pointer !important;
          border: 3px solid #ff0000 !important;
          position: relative !important;
        }

        .editor-content .youtube-video-placeholder:hover {
          transform: scale(1.02);
          transition: transform 0.2s ease;
        }

        .editor-content iframe {
          max-width: 100% !important;
          height: 450px !important;
          border: none !important;
          border-radius: 12px !important;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        }

        .editor-content:empty::before {
          content: attr(data-placeholder) "\\A\\A💡 يمكنك سحب الصور مباشرة إلى المحرر لرفعها";
          color: #999;
          font-style: italic;
          white-space: pre-line;
        }

        .editor-content h1 {
          font-size: 2em;
          font-weight: bold;
          margin: 0.67em 0;
        }

        .editor-content h2 {
          font-size: 1.5em;
          font-weight: bold;
          margin: 0.75em 0;
        }

        .editor-content h3 {
          font-size: 1.17em;
          font-weight: bold;
          margin: 0.83em 0;
        }

        .editor-content p {
          margin: 1em 0;
        }

        .editor-content ul, .editor-content ol {
          margin: 1em 0;
          padding-right: 2em;
          padding-left: 0;
        }

        .editor-content img {
          max-width: 100%;
          height: auto;
          border-radius: 4px;
        }

        .editor-content a {
          color: #007bff;
          text-decoration: underline;
        }

        .editor-content blockquote {
          border-right: 4px solid #007bff;
          border-left: none;
          margin: 1em 0;
          padding: 0.5em 1em;
          background-color: #f8f9fa;
          font-style: italic;
        }

        .editor-content table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
          direction: rtl;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          border-radius: 8px;
          overflow: hidden;
        }

        .editor-content table td,
        .editor-content table th {
          border: 1px solid #ddd;
          padding: 12px;
          text-align: right;
          vertical-align: top;
        }

        .editor-content table th {
          background-color: #f8f9fa;
          font-weight: bold;
        }

        .editor-content table tr:nth-child(even) {
          background-color: #f9f9f9;
        }

        .editor-content table tr:hover {
          background-color: #f0f8ff;
        }

        .editor-content pre {
          background-color: #f4f4f4;
          border: 1px solid #ddd;
          border-radius: 8px;
          padding: 16px;
          margin: 20px 0;
          overflow-x: auto;
          direction: ltr;
          text-align: left;
        }

        .editor-content code {
          font-family: 'Courier New', monospace;
          font-size: 14px;
          color: #333;
          background-color: #f8f9fa;
          padding: 2px 4px;
          border-radius: 3px;
        }

        .editor-content mark {
          background-color: #fff3cd;
          padding: 2px 4px;
          border-radius: 3px;
          color: #856404;
        }

        .pdf-uploader-section {
          border: 1px solid #e0e0e0;
          border-top: none;
          border-bottom: none;
          padding: 16px;
          background-color: #f8f9fa;
        }

        .youtube-inserter-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
          padding: 20px;
        }

        .youtube-inserter-modal {
          max-width: 500px;
          width: 100%;
          max-height: 90vh;
          overflow-y: auto;
        }

        .status-bar {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 16px;
          background-color: #f8f9fa;
          border-top: 1px solid #e0e0e0;
          border-radius: 0 0 8px 8px;
          font-size: 12px;
          color: #666;
        }

        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 768px) {
          .toolbar {
            padding: 8px;
            gap: 6px;
            flex-direction: column;
            align-items: stretch;
          }

          .toolbar-group {
            justify-content: center;
            flex-wrap: wrap;
            padding: 4px;
          }

          .toolbar button {
            min-width: 32px;
            height: 32px;
            font-size: 12px;
            padding: 0 6px;
          }

          .toolbar select {
            height: 32px;
            font-size: 12px;
            padding: 0 6px;
          }

          .color-picker {
            width: 32px !important;
            height: 32px !important;
          }

          .editor-content {
            padding: 12px;
            font-size: 14px;
          }

          .status-bar {
            flex-direction: column;
            gap: 4px;
            text-align: center;
          }

          .separator {
            display: none;
          }
        }

        @media (max-width: 480px) {
          .toolbar-group {
            gap: 2px;
          }

          .toolbar button {
            min-width: 28px;
            height: 28px;
            font-size: 11px;
            padding: 0 4px;
          }

          .toolbar select {
            height: 28px;
            font-size: 11px;
            padding: 0 4px;
          }

          .color-picker {
            width: 28px !important;
            height: 28px !important;
          }
        }
      `}</style>
    </div>
  );
};

export default WYSIWYGEditor;
