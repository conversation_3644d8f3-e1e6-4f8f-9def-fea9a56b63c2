// نظام تحليلات الأداء لـ Next.js 15

// التحقق من البيئة
const isBrowser = typeof window !== 'undefined';
const hasPerformanceObserver = isBrowser && typeof PerformanceObserver !== 'undefined';

// دالة لقياس Core Web Vitals
export function measureWebVitals() {
  if (!hasPerformanceObserver) return;
  
  // قياس Largest Contentful Paint (LCP)
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'largest-contentful-paint') {
        console.log('LCP:', entry.startTime);
        sendAnalytics('LCP', entry.startTime);
      }
    }
  });
  
  try {
    observer.observe({ entryTypes: ['largest-contentful-paint'] });
  } catch (e) {
    // المتصفح لا يدعم هذه الميزة
  }
  
  // قياس First Input Delay (FID)
  const fidObserver = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'first-input') {
        const fid = entry.processingStart - entry.startTime;
        console.log('FID:', fid);
        sendAnalytics('FID', fid);
      }
    }
  });
  
  try {
    fidObserver.observe({ entryTypes: ['first-input'] });
  } catch (e) {
    // المتصفح لا يدعم هذه الميزة
  }
  
  // قياس Cumulative Layout Shift (CLS)
  let clsValue = 0;
  let clsEntries = [];
  
  const clsObserver = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (!entry.hadRecentInput) {
        clsValue += entry.value;
        clsEntries.push(entry);
      }
    }
  });
  
  try {
    clsObserver.observe({ entryTypes: ['layout-shift'] });
  } catch (e) {
    // المتصفح لا يدعم هذه الميزة
  }
  
  // إرسال CLS عند مغادرة الصفحة
  window.addEventListener('beforeunload', () => {
    console.log('CLS:', clsValue);
    sendAnalytics('CLS', clsValue);
  });
}

// دالة لقياس أداء التحميل
export function measurePageLoad() {
  if (!isBrowser) return;
  
  window.addEventListener('load', () => {
    const navigation = performance.getEntriesByType('navigation')[0];
    
    if (navigation) {
      const metrics = {
        dns: navigation.domainLookupEnd - navigation.domainLookupStart,
        tcp: navigation.connectEnd - navigation.connectStart,
        request: navigation.responseStart - navigation.requestStart,
        response: navigation.responseEnd - navigation.responseStart,
        dom: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        load: navigation.loadEventEnd - navigation.loadEventStart,
        total: navigation.loadEventEnd - navigation.navigationStart,
      };
      
      console.log('Page Load Metrics:', metrics);
      sendAnalytics('pageLoad', metrics);
    }
  });
}

// دالة لقياس أداء API
export function measureAPIPerformance(url, startTime, endTime, status) {
  const duration = endTime - startTime;
  const metrics = {
    url,
    duration,
    status,
    timestamp: new Date().toISOString(),
  };
  
  console.log('API Performance:', metrics);
  sendAnalytics('apiPerformance', metrics);
}

// دالة لقياس استخدام الذاكرة
export function measureMemoryUsage() {
  if (!isBrowser || !performance.memory) return;
  
  const memory = {
    used: performance.memory.usedJSHeapSize,
    total: performance.memory.totalJSHeapSize,
    limit: performance.memory.jsHeapSizeLimit,
    timestamp: new Date().toISOString(),
  };
  
  console.log('Memory Usage:', memory);
  sendAnalytics('memoryUsage', memory);
}

// دالة لقياس تفاعل المستخدم
export function measureUserInteraction(action, element, duration = null) {
  const interaction = {
    action,
    element: element?.tagName || 'unknown',
    elementId: element?.id || null,
    elementClass: element?.className || null,
    duration,
    timestamp: new Date().toISOString(),
    url: window.location.pathname,
  };
  
  console.log('User Interaction:', interaction);
  sendAnalytics('userInteraction', interaction);
}

// دالة لقياس أخطاء JavaScript
export function measureJSErrors() {
  if (!isBrowser) return;
  
  window.addEventListener('error', (event) => {
    const error = {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack,
      timestamp: new Date().toISOString(),
      url: window.location.pathname,
    };
    
    console.error('JavaScript Error:', error);
    sendAnalytics('jsError', error);
  });
  
  window.addEventListener('unhandledrejection', (event) => {
    const error = {
      reason: event.reason?.message || event.reason,
      stack: event.reason?.stack,
      timestamp: new Date().toISOString(),
      url: window.location.pathname,
    };
    
    console.error('Unhandled Promise Rejection:', error);
    sendAnalytics('promiseRejection', error);
  });
}

// دالة لإرسال البيانات التحليلية
async function sendAnalytics(type, data) {
  if (process.env.NODE_ENV !== 'production') return;
  
  try {
    await fetch('/api/analytics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type,
        data,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
      }),
    });
  } catch (error) {
    console.error('Failed to send analytics:', error);
  }
}

// دالة لتهيئة جميع القياسات
export function initializeAnalytics() {
  if (!isBrowser) return;

  // تعطيل جميع القياسات مؤقتاً لحل مشكلة تسجيل الدخول
  return;

  measureWebVitals();
  measurePageLoad();
  measureJSErrors();

  // قياس استخدام الذاكرة كل 30 ثانية
  setInterval(measureMemoryUsage, 30000);

  // قياس تفاعلات المستخدم
  document.addEventListener('click', (event) => {
    measureUserInteraction('click', event.target);
  });

  document.addEventListener('scroll', () => {
    measureUserInteraction('scroll', document.body);
  });

  // قياس وقت البقاء في الصفحة
  const startTime = Date.now();
  window.addEventListener('beforeunload', () => {
    const timeOnPage = Date.now() - startTime;
    sendAnalytics('timeOnPage', {
      duration: timeOnPage,
      url: window.location.pathname,
    });
  });
}

// دالة لقياس أداء مكون React (مبسطة)
export function withPerformanceTracking(WrappedComponent, componentName) {
  if (!isBrowser) return WrappedComponent;

  return WrappedComponent; // إرجاع المكون كما هو للآن
}

// دالة لقياس أداء الصور
export function measureImagePerformance() {
  if (!isBrowser) return;
  
  const images = document.querySelectorAll('img');
  
  images.forEach((img, index) => {
    const startTime = performance.now();
    
    img.addEventListener('load', () => {
      const loadTime = performance.now() - startTime;
      
      sendAnalytics('imageLoad', {
        src: img.src,
        loadTime,
        width: img.naturalWidth,
        height: img.naturalHeight,
        index,
      });
    });
    
    img.addEventListener('error', () => {
      sendAnalytics('imageError', {
        src: img.src,
        index,
      });
    });
  });
}

// دالة لقياس أداء الخطوط
export function measureFontPerformance() {
  if (!isBrowser || !document.fonts) return;
  
  document.fonts.ready.then(() => {
    const loadedFonts = [];
    
    document.fonts.forEach((font) => {
      loadedFonts.push({
        family: font.family,
        style: font.style,
        weight: font.weight,
        status: font.status,
      });
    });
    
    sendAnalytics('fontsLoaded', {
      fonts: loadedFonts,
      count: loadedFonts.length,
    });
  });
}

// دالة لقياس أداء الشبكة
export function measureNetworkPerformance() {
  if (!isBrowser || !navigator.connection) return;
  
  const connection = navigator.connection;
  
  const networkInfo = {
    effectiveType: connection.effectiveType,
    downlink: connection.downlink,
    rtt: connection.rtt,
    saveData: connection.saveData,
  };
  
  sendAnalytics('networkInfo', networkInfo);
  
  // مراقبة تغييرات الشبكة
  connection.addEventListener('change', () => {
    const updatedInfo = {
      effectiveType: connection.effectiveType,
      downlink: connection.downlink,
      rtt: connection.rtt,
      saveData: connection.saveData,
      timestamp: new Date().toISOString(),
    };
    
    sendAnalytics('networkChange', updatedInfo);
  });
}

// تصدير جميع الدوال
export default {
  measureWebVitals,
  measurePageLoad,
  measureAPIPerformance,
  measureMemoryUsage,
  measureUserInteraction,
  measureJSErrors,
  initializeAnalytics,
  withPerformanceTracking,
  measureImagePerformance,
  measureFontPerformance,
  measureNetworkPerformance,
};
