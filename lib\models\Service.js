import mongoose from 'mongoose';

const serviceSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'عنوان الخدمة مطلوب'],
    trim: true,
    maxlength: [200, 'العنوان يجب أن يكون أقل من 200 حرف']
  },
  slug: {
    type: String,
    required: [true, 'الرابط المختصر مطلوب'],
    trim: true,
    lowercase: true,
    match: [/^[a-z0-9-]+$/, 'الرابط المختصر يجب أن يحتوي على أحرف إنجليزية وأرقام وشرطات فقط']
  },
  description: {
    type: String,
    required: [true, 'وصف الخدمة مطلوب'],
    trim: true,
    maxlength: [500, 'الوصف يجب أن يكون أقل من 500 حرف']
  },
  content: {
    type: String,
    required: [true, 'محتوى الخدمة مطلوب']
  },
  image: {
    type: String,
    trim: true
  },
  category: {
    type: String,
    required: [true, 'فئة الخدمة مطلوبة'],
    trim: true
  },
  tags: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  },
  featured: {
    type: Boolean,
    default: false
  },
  price: {
    type: String,
    trim: true
  },
  duration: {
    type: String,
    trim: true
  },
  requirements: {
    type: String,
    trim: true
  },
  benefits: [{
    type: String,
    trim: true
  }],
  process: [{
    step: {
      type: Number,
      required: true
    },
    title: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    }
  }],
  faqs: [{
    question: {
      type: String,
      required: true,
      trim: true
    },
    answer: {
      type: String,
      required: true,
      trim: true
    }
  }],
  views: {
    type: Number,
    default: 0
  },
  inquiries: {
    type: Number,
    default: 0
  },
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0
    }
  },
  meta: {
    title: {
      type: String,
      trim: true,
      maxlength: [60, 'عنوان SEO يجب أن يكون أقل من 60 حرف']
    },
    description: {
      type: String,
      trim: true,
      maxlength: [160, 'وصف SEO يجب أن يكون أقل من 160 حرف']
    },
    keywords: {
      type: String,
      trim: true
    }
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
});

// فهرسة للبحث والأداء
serviceSchema.index({ slug: 1 }, { unique: true });
serviceSchema.index({ status: 1 });
serviceSchema.index({ category: 1 });
serviceSchema.index({ featured: 1 });
serviceSchema.index({ created_at: -1 });
serviceSchema.index({ 
  title: 'text', 
  description: 'text', 
  content: 'text',
  tags: 'text'
}, {
  weights: {
    title: 10,
    description: 5,
    content: 1,
    tags: 3
  }
});

// طرق النموذج
serviceSchema.methods.incrementViews = function() {
  return this.updateOne({ $inc: { views: 1 } });
};

serviceSchema.methods.incrementInquiries = function() {
  return this.updateOne({ $inc: { inquiries: 1 } });
};

serviceSchema.methods.updateRating = function(newRating) {
  const currentAverage = this.rating.average;
  const currentCount = this.rating.count;
  
  const newCount = currentCount + 1;
  const newAverage = ((currentAverage * currentCount) + newRating) / newCount;
  
  return this.updateOne({
    'rating.average': Math.round(newAverage * 10) / 10,
    'rating.count': newCount
  });
};

// طرق ثابتة
serviceSchema.statics.findPublished = function() {
  return this.find({ status: 'published' }).sort({ created_at: -1 });
};

serviceSchema.statics.findFeatured = function() {
  return this.find({ status: 'published', featured: true }).sort({ created_at: -1 });
};

serviceSchema.statics.findByCategory = function(category) {
  return this.find({ status: 'published', category }).sort({ created_at: -1 });
};

serviceSchema.statics.searchServices = function(query) {
  return this.find(
    { 
      $text: { $search: query },
      status: 'published'
    },
    { score: { $meta: 'textScore' } }
  ).sort({ score: { $meta: 'textScore' } });
};

serviceSchema.statics.getPopular = function(limit = 10) {
  return this.find({ status: 'published' })
    .sort({ views: -1, inquiries: -1 })
    .limit(limit);
};

serviceSchema.statics.getTopRated = function(limit = 10) {
  return this.find({ 
    status: 'published',
    'rating.count': { $gte: 1 }
  })
    .sort({ 'rating.average': -1, 'rating.count': -1 })
    .limit(limit);
};

// تصدير النموذج
const Service = mongoose.models.Service || mongoose.model('Service', serviceSchema);

export default Service;
