import { connectToDatabase } from './mongodb.js';
import {
  User,
  Article,
  Service,
  Category,
  Slide,
  Page,
  ContactMessage,
  TeamMember,
  ElectronicService,
  Expertise,
  Setting
} from './models/index.js';

// خريطة النماذج
const modelMap = {
  users: User,
  articles: Article,
  services: Service,
  categories: Category,
  slides: Slide,
  pages: Page,
  contact_messages: ContactMessage,
  team_members: TeamMember,
  electronic_services: ElectronicService,
  expertise: Expertise,
  settings: Setting
};

// دالة للحصول على النموذج
function getModel(table) {
  const model = modelMap[table];
  if (!model) {
    throw new Error(`Model for table '${table}' not found`);
  }
  return model;
}

// قراءة البيانات من MongoDB
export const readData = async (table) => {
  try {
    await connectToDatabase();
    const Model = getModel(table);
    const data = await Model.find({}).lean();
    
    // تحويل _id إلى id للتوافق مع النظام القديم
    return data.map(item => ({
      ...item,
      id: item._id.toString(),
      _id: undefined
    }));
  } catch (error) {
    console.error(`Error reading ${table}:`, error);
    return [];
  }
};

// كتابة البيانات إلى MongoDB
export const writeData = async (table, data) => {
  try {
    await connectToDatabase();
    const Model = getModel(table);
    
    // حذف جميع البيانات الموجودة
    await Model.deleteMany({});
    
    // إدراج البيانات الجديدة
    if (data && data.length > 0) {
      // تحويل id إلى _id إذا لزم الأمر
      const processedData = data.map(item => {
        const { id, ...rest } = item;
        return rest;
      });
      
      await Model.insertMany(processedData);
    }
    
    return true;
  } catch (error) {
    console.error(`Error writing ${table}:`, error);
    return false;
  }
};

// إنشاء ID جديد (MongoDB يقوم بذلك تلقائياً)
export const generateId = () => {
  return new Date().getTime().toString();
};

// تهيئة قاعدة البيانات
export const initDatabase = async () => {
  try {
    await connectToDatabase();
    
    // تهيئة الإعدادات الافتراضية
    await Setting.initializeDefaults();
    
    console.log('MongoDB database initialized successfully');
    return true;
  } catch (error) {
    console.error('Error initializing MongoDB database:', error);
    return false;
  }
};

// محاكاة عمليات قاعدة البيانات للتوافق مع النظام القديم
export const db = {
  prepare: (query) => {
    return {
      run: async (...params) => {
        await connectToDatabase();
        
        try {
          // محاكاة INSERT
          if (query.includes('INSERT INTO')) {
            const tableMatch = query.match(/INSERT INTO (\w+)/);
            if (!tableMatch) throw new Error('Invalid INSERT query');
            
            const tableName = tableMatch[1];
            const Model = getModel(tableName);
            
            // إنشاء كائن البيانات حسب الجدول
            let newItem = {};
            
            if (tableName === 'slides') {
              const [title, subtitle, description, image, cta_text, cta_link, order_index, status] = params;
              newItem = { title, subtitle, description, image, cta_text, cta_link, order_index, status };
            } else if (tableName === 'articles') {
              const [title, slug, excerpt, content, image, author, category, tags, status, featured] = params;
              newItem = { title, slug, excerpt, content, image, author, category, tags, status, featured: !!featured };
            } else if (tableName === 'users') {
              const [username, email, password, role] = params;
              newItem = { username, email, password, role: role || 'admin' };
            } else if (tableName === 'services') {
              const [title, slug, description, content, image, category, tags, status, featured, price, duration, requirements] = params;
              newItem = { title, slug, description, content, image, category, tags, status, featured: !!featured, price, duration, requirements };
            } else if (tableName === 'pages') {
              const [title, slug, content, meta_title, meta_description, status, template, featured_image] = params;
              newItem = { title, slug, content, meta: { title: meta_title, description: meta_description }, status, template, featured_image };
            } else if (tableName === 'team_members') {
              const [name, position, bio, image, email, phone, specializations, order, status] = params;
              newItem = { name, position, bio, image, email, phone, specializations: specializations ? specializations.split(',') : [], order, status };
            } else if (tableName === 'contact_messages') {
              const [name, email, phone, subject, message, status] = params;
              newItem = { name, email, phone, subject, message, status: status || 'unread' };
            }
            
            const result = await Model.create(newItem);
            return { lastInsertRowid: result._id.toString() };
          }
          
          // محاكاة UPDATE
          if (query.includes('UPDATE')) {
            const tableMatch = query.match(/UPDATE (\w+)/);
            const whereMatch = query.match(/WHERE (.+)/);
            
            if (!tableMatch) throw new Error('Invalid UPDATE query');
            
            const tableName = tableMatch[1];
            const Model = getModel(tableName);
            
            // استخراج شروط WHERE
            let filter = {};
            if (whereMatch) {
              const whereClause = whereMatch[1];
              if (whereClause.includes('id = ?')) {
                filter._id = params[params.length - 1];
              }
            }
            
            // إنشاء كائن التحديث
            let updateData = {};
            
            // استخراج الحقول من الاستعلام
            const setMatch = query.match(/SET (.+) WHERE/);
            if (setMatch) {
              const setClause = setMatch[1];
              const fields = setClause.split(',').map(f => f.trim().split('=')[0].trim());
              
              fields.forEach((field, index) => {
                if (params[index] !== undefined) {
                  updateData[field] = params[index];
                }
              });
            }
            
            updateData.updated_at = new Date();
            
            const result = await Model.updateOne(filter, updateData);
            return { changes: result.modifiedCount };
          }
          
          // محاكاة DELETE
          if (query.includes('DELETE FROM')) {
            const tableMatch = query.match(/DELETE FROM (\w+)/);
            const whereMatch = query.match(/WHERE (.+)/);
            
            if (!tableMatch) throw new Error('Invalid DELETE query');
            
            const tableName = tableMatch[1];
            const Model = getModel(tableName);
            
            let filter = {};
            if (whereMatch) {
              const whereClause = whereMatch[1];
              if (whereClause.includes('id = ?')) {
                filter._id = params[0];
              }
            }
            
            const result = await Model.deleteOne(filter);
            return { changes: result.deletedCount };
          }
          
        } catch (error) {
          console.error('Database operation error:', error);
          throw error;
        }
      },
      
      get: async (...params) => {
        await connectToDatabase();
        
        try {
          // محاكاة SELECT
          if (query.includes('SELECT')) {
            const tableMatch = query.match(/FROM (\w+)/);
            if (!tableMatch) throw new Error('Invalid SELECT query');
            
            const tableName = tableMatch[1];
            const Model = getModel(tableName);
            
            let filter = {};
            let options = {};
            
            // استخراج شروط WHERE
            const whereMatch = query.match(/WHERE (.+?)(?:\s+ORDER|\s+LIMIT|$)/);
            if (whereMatch) {
              const whereClause = whereMatch[1];
              if (whereClause.includes('id = ?')) {
                filter._id = params[0];
              } else if (whereClause.includes('slug = ?')) {
                filter.slug = params[0];
              } else if (whereClause.includes('email = ?')) {
                filter.email = params[0];
              } else if (whereClause.includes('status = ?')) {
                filter.status = params[0];
              }
            }
            
            // استخراج ORDER BY
            const orderMatch = query.match(/ORDER BY (.+?)(?:\s+LIMIT|$)/);
            if (orderMatch) {
              const orderClause = orderMatch[1];
              if (orderClause.includes('DESC')) {
                const field = orderClause.replace(/\s+DESC.*/, '').trim();
                options.sort = { [field]: -1 };
              } else {
                const field = orderClause.replace(/\s+ASC.*/, '').trim();
                options.sort = { [field]: 1 };
              }
            }
            
            // استخراج LIMIT
            const limitMatch = query.match(/LIMIT (\d+)/);
            if (limitMatch) {
              options.limit = parseInt(limitMatch[1]);
            }
            
            const result = await Model.findOne(filter, null, options).lean();
            
            if (result) {
              return {
                ...result,
                id: result._id.toString(),
                _id: undefined
              };
            }
            
            return null;
          }
        } catch (error) {
          console.error('Database query error:', error);
          return null;
        }
      },
      
      all: async (...params) => {
        await connectToDatabase();
        
        try {
          // محاكاة SELECT للحصول على جميع النتائج
          if (query.includes('SELECT')) {
            const tableMatch = query.match(/FROM (\w+)/);
            if (!tableMatch) throw new Error('Invalid SELECT query');
            
            const tableName = tableMatch[1];
            const Model = getModel(tableName);
            
            let filter = {};
            let options = {};
            
            // استخراج شروط WHERE
            const whereMatch = query.match(/WHERE (.+?)(?:\s+ORDER|\s+LIMIT|$)/);
            if (whereMatch) {
              const whereClause = whereMatch[1];
              if (whereClause.includes('status = ?')) {
                filter.status = params[0];
              } else if (whereClause.includes('category = ?')) {
                filter.category = params[0];
              } else if (whereClause.includes('featured = ?')) {
                filter.featured = params[0] === 1 || params[0] === true;
              }
            }
            
            // استخراج ORDER BY
            const orderMatch = query.match(/ORDER BY (.+?)(?:\s+LIMIT|$)/);
            if (orderMatch) {
              const orderClause = orderMatch[1];
              if (orderClause.includes('DESC')) {
                const field = orderClause.replace(/\s+DESC.*/, '').trim();
                options.sort = { [field]: -1 };
              } else {
                const field = orderClause.replace(/\s+ASC.*/, '').trim();
                options.sort = { [field]: 1 };
              }
            }
            
            // استخراج LIMIT
            const limitMatch = query.match(/LIMIT (\d+)/);
            if (limitMatch) {
              options.limit = parseInt(limitMatch[1]);
            }
            
            const results = await Model.find(filter, null, options).lean();
            
            return results.map(item => ({
              ...item,
              id: item._id.toString(),
              _id: undefined
            }));
          }
        } catch (error) {
          console.error('Database query error:', error);
          return [];
        }
      }
    };
  }
};

// تصدير الدوال للتوافق مع النظام القديم
export default {
  readData,
  writeData,
  generateId,
  initDatabase,
  db
};
