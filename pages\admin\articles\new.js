import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '../../../components/admin/AdminLayout';
import WYSIWYGEditor from '../../../components/admin/WYSIWYGEditor';
import FontSelector from '../../../components/admin/FontSelector';
import ColorPicker from '../../../components/admin/ColorPicker';
import ContentPreview from '../../../components/admin/ContentPreview';
import Link from 'next/link';
import {
  FaSave,
  FaArrowLeft,
  FaImage,
  FaUpload,
  FaStar,
  FaEye,
  FaTimes,
  FaCheck,
  FaExclamationTriangle,
  FaInfoCircle,
  FaUser,
  FaTag,
  FaCalendar,
  FaGlobe,
  FaClock,
  FaFileAlt,
  FaCog,
  FaFont,
  FaFilePdf
} from 'react-icons/fa';

export default function NewArticle() {
  const router = useRouter();
  const fileInputRef = useRef(null);
  
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    image: '',
    author: '',
    category: '',
    tags: '',
    status: 'draft',
    featured: false,
    metaTitle: '',
    metaDescription: '',
    publishDate: new Date().toISOString().split('T')[0],
    contentFont: 'Cairo',
    contentTextColor: '#1f2937',
    contentBgColor: '#ffffff',
    pdf_file: null
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [imagePreview, setImagePreview] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});
  const [savedDraft, setSavedDraft] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [categories, setCategories] = useState([]);
  const [pdfUploading, setPdfUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch categories
  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      const data = await response.json();
      if (data.success) {
        setCategories(data.categories);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  // Auto-save functionality
  useEffect(() => {
    if (autoSaveEnabled && (formData.title || formData.content)) {
      const timer = setTimeout(() => {
        saveDraft();
      }, 30000); // Auto-save every 30 seconds

      return () => {
        clearTimeout(timer);
      };
    }
  }, [formData, autoSaveEnabled]);

  // Load draft on component mount
  useEffect(() => {
    loadDraft();
  }, []);

  const validateField = (name, value) => {
    const errors = { ...validationErrors };
    
    switch (name) {
      case 'title':
        if (!value.trim()) {
          errors.title = 'العنوان مطلوب';
        } else if (value.length < 5) {
          errors.title = 'العنوان يجب أن يكون 5 أحرف على الأقل';
        } else if (value.length > 100) {
          errors.title = 'العنوان يجب أن يكون أقل من 100 حرف';
        } else {
          delete errors.title;
        }
        break;
      case 'content':
        const textContent = value.replace(/<[^>]*>/g, '');
        if (!textContent.trim()) {
          errors.content = 'المحتوى مطلوب';
        } else if (textContent.length < 50) {
          errors.content = 'المحتوى يجب أن يكون 50 حرف على الأقل';
        } else {
          delete errors.content;
        }
        break;
      case 'excerpt':
        if (value && value.length > 300) {
          errors.excerpt = 'الملخص يجب أن يكون أقل من 300 حرف';
        } else {
          delete errors.excerpt;
        }
        break;
      default:
        break;
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;
    
    setFormData(prev => ({
      ...prev,
      [name]: newValue
    }));
    
    setError('');
    setSuccess('');
    
    // Validate field
    validateField(name, newValue);

    // Auto-generate slug from title
    if (name === 'title') {
      const slug = value.toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setFormData(prev => ({
        ...prev,
        slug: slug,
        metaTitle: value
      }));
    }

    // Auto-update meta description from excerpt
    if (name === 'excerpt') {
      setFormData(prev => ({
        ...prev,
        metaDescription: value.substring(0, 160)
      }));
    }
  };

  const handleContentChange = (content) => {
    setFormData(prev => ({
      ...prev,
      content
    }));
    validateField('content', content);
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        setError('يرجى اختيار ملف صورة صحيح');
        return;
      }
      
      if (file.size > 5 * 1024 * 1024) {
        setError('حجم الصورة يجب أن يكون أقل من 5 ميجابايت');
        return;
      }

      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
        setFormData(prev => ({
          ...prev,
          image: reader.result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setImagePreview(null);
    setFormData(prev => ({
      ...prev,
      image: ''
    }));
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handlePDFUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (file.type !== 'application/pdf') {
      setError('يُسمح فقط بملفات PDF');
      return;
    }

    // التحقق من حجم الملف (20MB)
    if (file.size > 20 * 1024 * 1024) {
      setError('حجم الملف يجب أن يكون أقل من 20 ميجابايت');
      return;
    }

    setPdfUploading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('pdf', file);

      // إضافة timeout أطول للملفات الكبيرة
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 ثانية

      const response = await fetch('/api/upload/pdf', {
        method: 'POST',
        body: formData,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const result = await response.json();

      if (result.success) {
        setFormData(prev => ({
          ...prev,
          pdf_file: result.file
        }));
        setSuccess('تم رفع ملف PDF بنجاح');
      } else {
        setError(result.error || 'خطأ في رفع الملف');
      }
    } catch (error) {
      console.error('Upload error:', error);
      if (error.name === 'AbortError') {
        setError('انتهت مهلة رفع الملف. يرجى المحاولة مرة أخرى.');
      } else if (error.message.includes('Failed to fetch')) {
        setError('خطأ في الاتصال. تحقق من الإنترنت وحاول مرة أخرى.');
      } else {
        setError('خطأ في رفع الملف: ' + (error.message || 'خطأ غير معروف'));
      }
    } finally {
      setPdfUploading(false);
    }
  };

  const removePDF = () => {
    setFormData(prev => ({
      ...prev,
      pdf_file: null
    }));
  };

  const saveDraft = async () => {
    try {
      localStorage.setItem('articleDraft', JSON.stringify(formData));
      setSavedDraft(true);
      setTimeout(() => setSavedDraft(false), 2000);
    } catch (error) {
      console.error('خطأ في حفظ المسودة:', error);
    }
  };

  const loadDraft = () => {
    try {
      const draft = localStorage.getItem('articleDraft');
      if (draft) {
        const draftData = JSON.parse(draft);
        setFormData(draftData);
        if (draftData.image) {
          setImagePreview(draftData.image);
        }
      }
    } catch (error) {
      console.error('خطأ في تحميل المسودة:', error);
    }
  };

  const clearDraft = () => {
    localStorage.removeItem('articleDraft');
  };

  const validateForm = () => {
    const errors = {};
    
    if (!formData.title.trim()) errors.title = 'العنوان مطلوب';
    if (!formData.content.trim()) errors.content = 'المحتوى مطلوب';
    if (formData.title.length < 5) errors.title = 'العنوان قصير جداً';
    
    const textContent = formData.content.replace(/<[^>]*>/g, '');
    if (textContent.length < 50) errors.content = 'المحتوى قصير جداً';
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e, shouldRedirect = true) => {
    if (e && e.preventDefault) {
      e.preventDefault();
    }

    // منع الإرسال المتعدد
    if (isSubmitting) {
      return;
    }

    if (!validateForm()) {
      setError('يرجى تصحيح الأخطاء قبل الحفظ');
      return;
    }

    setIsSubmitting(true);
    setLoading(true);
    setError('');

    try {
      // إرسال البيانات إلى API
      const response = await fetch('/api/articles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('تم إنشاء المقال بنجاح');
        clearDraft();

        if (shouldRedirect) {
          setTimeout(() => {
            router.push('/admin/articles');
          }, 1500);
        }
      } else {
        setError(result.error || 'حدث خطأ في إنشاء المقال');
      }
    } catch (error) {
      console.error('Submit error:', error);
      setError('حدث خطأ في إنشاء المقال');
    } finally {
      setLoading(false);
      setIsSubmitting(false);
    }
  };

  const getProgressPercentage = () => {
    let completed = 0;
    const total = 6;
    
    if (formData.title) completed++;
    if (formData.content) completed++;
    if (formData.excerpt) completed++;
    if (formData.category) completed++;
    if (formData.author) completed++;
    if (formData.image) completed++;
    
    return Math.round((completed / total) * 100);
  };

  const getWordCount = () => {
    return formData.content.replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0).length;
  };

  const getReadingTime = () => {
    const wordsPerMinute = 200;
    const words = getWordCount();
    return Math.ceil(words / wordsPerMinute);
  };

  return (
    <AdminLayout title="إضافة مقال جديد">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Enhanced Header */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl shadow-lg text-white p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex-1">
              <h1 className="text-2xl lg:text-3xl font-bold mb-2">✍️ إضافة مقال جديد</h1>
              <p className="text-primary-100">إنشاء محتوى قانوني متميز وإعلامي للموقع</p>
            </div>
            
            <div className="flex items-center gap-3">
              {/* Progress Indicator */}
              <div className="hidden lg:flex items-center gap-3 bg-white/10 rounded-lg px-4 py-2">
                <FaFileAlt className="text-primary-200" />
                <div className="text-sm">
                  <div className="flex items-center gap-2 mb-1">
                    <span>التقدم:</span>
                    <span className="font-bold">{getProgressPercentage()}%</span>
                  </div>
                  <div className="w-24 bg-white/20 rounded-full h-1">
                    <div 
                      className="bg-white h-1 rounded-full transition-all duration-300"
                      style={{ width: `${getProgressPercentage()}%` }}
                    ></div>
                  </div>
                </div>
              </div>
              
              {/* Auto-save indicator */}
              <div className="flex items-center gap-2 bg-white/10 rounded-lg px-3 py-2">
                <FaClock className="text-primary-200" />
                <span className="text-sm">
                  {savedDraft ? 'تم الحفظ!' : 'حفظ تلقائي'}
                </span>
              </div>
              
              {/* Actions */}
              <button
                onClick={saveDraft}
                className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center text-sm"
              >
                <FaSave className="ml-2" />
                حفظ مسودة
              </button>
              
              <Link href="/admin/articles">
                <button className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center text-sm">
                  <FaArrowLeft className="ml-2" />
                  العودة
                </button>
              </Link>
            </div>
          </div>
        </div>

        {/* Alerts */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 flex items-start">
            <FaExclamationTriangle className="text-red-500 ml-3 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-800 font-medium">خطأ في الحفظ</h4>
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-4 flex items-start">
            <FaCheck className="text-green-500 ml-3 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-green-800 font-medium">تم بنجاح</h4>
              <p className="text-green-700 text-sm">{success}</p>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Form Content */}
          <div className="lg:col-span-3">
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-primary-100 rounded-xl flex items-center justify-center ml-3">
                    <span className="text-primary-600 font-bold text-lg">1</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">المعلومات الأساسية</h3>
                    <p className="text-sm text-gray-600">العنوان والرابط المختصر</p>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Title */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      عنوان المقال *
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-3 pr-12 border rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 transition-all duration-200 ${
                          validationErrors.title
                            ? 'border-red-500 bg-red-50'
                            : formData.title
                            ? 'border-green-500 bg-green-50'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                        placeholder="أدخل عنوان المقال..."
                        required
                      />
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                        {formData.title && !validationErrors.title && (
                          <FaCheck className="text-green-500" />
                        )}
                        {validationErrors.title && (
                          <FaExclamationTriangle className="text-red-500" />
                        )}
                      </div>
                    </div>
                    {validationErrors.title && (
                      <p className="mt-2 text-sm text-red-600 flex items-center">
                        <FaExclamationTriangle className="ml-1" />
                        {validationErrors.title}
                      </p>
                    )}
                    <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
                      <span>{formData.title.length}/100 حرف</span>
                      {formData.title && (
                        <span className="text-green-600">✓ العنوان جاهز</span>
                      )}
                    </div>
                  </div>

                  {/* Slug */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الرابط المختصر (Slug)
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        name="slug"
                        value={formData.slug}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 transition-all duration-200 hover:border-gray-400"
                        placeholder="article-slug"
                      />
                      <FaGlobe className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    </div>
                    <p className="text-xs text-gray-500 mt-2 flex items-center">
                      <FaInfoCircle className="ml-1" />
                      سيتم إنشاؤه تلقائياً من العنوان إذا تُرك فارغاً
                    </p>
                  </div>
                </div>
              </div>

              {/* Content Section */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-primary-100 rounded-xl flex items-center justify-center ml-3">
                    <span className="text-primary-600 font-bold text-lg">2</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">المحتوى والملخص</h3>
                    <p className="text-sm text-gray-600">النص الرئيسي والملخص</p>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Excerpt */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      ملخص المقال
                    </label>
                    <div className="relative">
                      <textarea
                        name="excerpt"
                        value={formData.excerpt}
                        onChange={handleInputChange}
                        rows="3"
                        className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 transition-all duration-200 resize-none ${
                          validationErrors.excerpt
                            ? 'border-red-500 bg-red-50'
                            : formData.excerpt
                            ? 'border-green-500 bg-green-50'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                        placeholder="ملخص قصير وجذاب عن المقال..."
                      />
                    </div>
                    {validationErrors.excerpt && (
                      <p className="mt-2 text-sm text-red-600 flex items-center">
                        <FaExclamationTriangle className="ml-1" />
                        {validationErrors.excerpt}
                      </p>
                    )}
                    <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
                      <span>{formData.excerpt.length}/300 حرف</span>
                      {formData.excerpt && (
                        <span className="text-green-600">✓ الملخص جاهز</span>
                      )}
                    </div>
                  </div>

                  {/* Content */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      محتوى المقال *
                    </label>
                    <div className={`rounded-xl overflow-hidden ${
                      validationErrors.content
                        ? 'ring-2 ring-red-500'
                        : formData.content
                        ? 'ring-2 ring-green-500'
                        : ''
                    }`}>
                      <WYSIWYGEditor
                        value={formData.content}
                        onChange={handleContentChange}
                        placeholder="ابدأ في كتابة محتوى المقال... يمكنك استخدام التنسيقات المختلفة"
                        height={500}
                      />
                    </div>
                    {validationErrors.content && (
                      <p className="mt-2 text-sm text-red-600 flex items-center">
                        <FaExclamationTriangle className="ml-1" />
                        {validationErrors.content}
                      </p>
                    )}
                    <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center gap-4">
                        <span>{formData.content.replace(/<[^>]*>/g, '').length} حرف</span>
                        <span>{getWordCount()} كلمة</span>
                        <span>⏱️ {getReadingTime()} دقيقة قراءة</span>
                      </div>
                      {formData.content && (
                        <span className="text-green-600">✓ المحتوى جاهز</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Image Upload */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FaImage className="ml-2 text-primary-600" />
                الصورة المميزة
              </h3>

              <div className="space-y-4">
                {imagePreview ? (
                  <div className="relative group">
                    <img
                      src={imagePreview}
                      alt="معاينة الصورة"
                      className="w-full h-48 object-cover rounded-xl"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-xl flex items-center justify-center">
                      <button
                        type="button"
                        onClick={removeImage}
                        className="bg-red-500 text-white p-3 rounded-full hover:bg-red-600 transition-colors duration-200"
                      >
                        <FaTimes />
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-primary-500 transition-colors duration-200 cursor-pointer">
                    <FaImage className="text-4xl text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-4">اسحب الصورة هنا أو انقر للاختيار</p>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                      id="image-upload"
                    />
                    <label
                      htmlFor="image-upload"
                      className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200 cursor-pointer inline-flex items-center"
                    >
                      <FaUpload className="ml-2" />
                      اختر صورة
                    </label>
                  </div>
                )}

                <p className="text-xs text-gray-500">
                  الحد الأقصى: 5 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF
                </p>
              </div>
            </div>

            {/* Content Formatting */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FaFont className="ml-2 text-primary-600" />
                تنسيق المحتوى
              </h3>

              <div className="space-y-4">
                {/* اختيار الخط */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    خط المحتوى
                  </label>
                  <FontSelector
                    selectedFont={formData.contentFont}
                    onFontChange={(font) => setFormData(prev => ({ ...prev, contentFont: font }))}
                  />
                </div>

                {/* لون النص */}
                <div>
                  <ColorPicker
                    selectedColor={formData.contentTextColor}
                    onColorChange={(color) => setFormData(prev => ({ ...prev, contentTextColor: color }))}
                    label="لون النص"
                    type="text"
                  />
                </div>

                {/* لون الخلفية */}
                <div>
                  <ColorPicker
                    selectedColor={formData.contentBgColor}
                    onColorChange={(color) => setFormData(prev => ({ ...prev, contentBgColor: color }))}
                    label="لون الخلفية"
                    type="background"
                  />
                </div>
              </div>
            </div>

            {/* PDF Upload */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FaFilePdf className="ml-2 text-red-600" />
                ملف PDF مرفق
              </h3>

              {!formData.pdf_file ? (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors duration-200">
                  <FaFilePdf className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-600 mb-4">اختر ملف PDF لإرفاقه مع المقال</p>
                  <input
                    type="file"
                    accept=".pdf"
                    onChange={handlePDFUpload}
                    className="hidden"
                    id="pdf-upload"
                    disabled={pdfUploading}
                  />
                  <label
                    htmlFor="pdf-upload"
                    className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors duration-200 cursor-pointer ${
                      pdfUploading
                        ? 'bg-gray-400 text-white cursor-not-allowed'
                        : 'bg-red-600 text-white hover:bg-red-700'
                    }`}
                  >
                    {pdfUploading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                        جاري الرفع...
                      </>
                    ) : (
                      <>
                        <FaUpload className="ml-2" />
                        اختر ملف PDF
                      </>
                    )}
                  </label>
                  <p className="text-xs text-gray-500 mt-2">الحد الأقصى: 20 ميجابايت</p>
                </div>
              ) : (
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <FaFilePdf className="text-red-600 text-xl" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{formData.pdf_file.name}</p>
                        <p className="text-xs text-gray-500">
                          {(formData.pdf_file.size / 1024 / 1024).toFixed(2)} ميجابايت
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <a
                        href={formData.pdf_file.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 text-blue-600 hover:bg-blue-100 rounded-full transition-colors"
                        title="معاينة"
                      >
                        <FaEye />
                      </a>
                      <button
                        onClick={removePDF}
                        className="p-2 text-red-600 hover:bg-red-100 rounded-full transition-colors"
                        title="حذف"
                      >
                        <FaTimes />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Article Settings */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FaCog className="ml-2 text-primary-600" />
                إعدادات المقال
              </h3>

              <div className="space-y-4">
                {/* Author */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الكاتب
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      name="author"
                      value={formData.author}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="اسم كاتب المقال"
                    />
                    <FaUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                </div>

                {/* Category */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    التصنيف
                  </label>
                  <select
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="">اختر التصنيف</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.name}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Tags */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الكلمات المفتاحية
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      name="tags"
                      value={formData.tags}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="قانون، شركات، استشارات"
                    />
                    <FaTag className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">افصل بين الكلمات بفواصل</p>
                </div>

                {/* Publish Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    تاريخ النشر
                  </label>
                  <div className="relative">
                    <input
                      type="date"
                      name="publishDate"
                      value={formData.publishDate}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                    <FaCalendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                </div>

                {/* Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    حالة النشر
                  </label>
                  <select
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="draft">مسودة</option>
                    <option value="published">منشور</option>
                  </select>
                </div>

                {/* Featured */}
                <div className="flex items-center p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                  <input
                    type="checkbox"
                    name="featured"
                    id="featured"
                    checked={formData.featured}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                  />
                  <label htmlFor="featured" className="mr-3 flex items-center text-yellow-800 font-medium">
                    <FaStar className="text-yellow-500 ml-2" />
                    جعل هذا المقال مميزاً
                  </label>
                </div>
              </div>
            </div>

            {/* Progress Summary */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">ملخص التقدم</h3>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">العنوان</span>
                  {formData.title ? (
                    <FaCheck className="text-green-500" />
                  ) : (
                    <div className="w-4 h-4 border-2 border-gray-300 rounded-full"></div>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">المحتوى</span>
                  {formData.content ? (
                    <FaCheck className="text-green-500" />
                  ) : (
                    <div className="w-4 h-4 border-2 border-gray-300 rounded-full"></div>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">الملخص</span>
                  {formData.excerpt ? (
                    <FaCheck className="text-green-500" />
                  ) : (
                    <div className="w-4 h-4 border-2 border-gray-300 rounded-full"></div>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">التصنيف</span>
                  {formData.category ? (
                    <FaCheck className="text-green-500" />
                  ) : (
                    <div className="w-4 h-4 border-2 border-gray-300 rounded-full"></div>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">الكاتب</span>
                  {formData.author ? (
                    <FaCheck className="text-green-500" />
                  ) : (
                    <div className="w-4 h-4 border-2 border-gray-300 rounded-full"></div>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">الصورة</span>
                  {formData.image ? (
                    <FaCheck className="text-green-500" />
                  ) : (
                    <div className="w-4 h-4 border-2 border-gray-300 rounded-full"></div>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">ملف PDF</span>
                  {formData.pdf_file ? (
                    <FaCheck className="text-green-500" />
                  ) : (
                    <div className="w-4 h-4 border-2 border-gray-300 rounded-full"></div>
                  )}
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">التقدم الإجمالي</span>
                  <span className="text-sm font-bold text-primary-600">{getProgressPercentage()}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${getProgressPercentage()}%` }}
                  ></div>
                </div>
              </div>
            </div>

            {/* Content Preview */}
            {formData.content && (
              <ContentPreview
                content={formData.content}
                title={formData.title}
                excerpt={formData.excerpt}
              />
            )}
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex flex-col sm:flex-row gap-4 justify-end">
            <Link href="/admin/articles">
              <button
                type="button"
                className="w-full sm:w-auto bg-gray-100 text-gray-700 px-6 py-3 rounded-xl hover:bg-gray-200 transition-colors duration-200 font-medium"
              >
                إلغاء
              </button>
            </Link>

            <button
              type="button"
              onClick={() => {
                setFormData(prev => ({ ...prev, status: 'draft' }));
                handleSubmit({ preventDefault: () => {} }, false); // لا تعيد التوجيه للمسودة
              }}
              className="w-full sm:w-auto bg-yellow-100 text-yellow-700 px-6 py-3 rounded-xl hover:bg-yellow-200 transition-colors duration-200 font-medium"
            >
              حفظ كمسودة
            </button>

            <button
              type="button"
              onClick={() => {
                setFormData(prev => ({ ...prev, status: 'published' }));
                handleSubmit({ preventDefault: () => {} }, true); // أعد التوجيه للنشر
              }}
              disabled={loading}
              className={`w-full sm:w-auto px-6 py-3 rounded-xl font-medium transition-all duration-200 flex items-center justify-center ${
                loading
                  ? 'bg-gray-400 cursor-not-allowed text-white'
                  : 'bg-primary-600 hover:bg-primary-700 text-white shadow-lg hover:shadow-xl'
              }`}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
                  جاري النشر...
                </>
              ) : (
                <>
                  <FaSave className="ml-2" />
                  نشر المقال
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
