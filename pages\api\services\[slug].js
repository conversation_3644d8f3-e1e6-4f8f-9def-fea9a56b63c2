import { readData } from '../../../lib/database';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }

  const { slug } = req.query;

  if (!slug) {
    return res.status(400).json({
      success: false,
      message: 'معرف الخدمة مطلوب'
    });
  }

  try {
    const services = readData('services');
    const service = services.find(service => 
      service.slug === slug && service.status === 'published'
    );

    if (!service) {
      return res.status(404).json({
        success: false,
        message: 'الخدمة غير موجودة أو غير منشورة'
      });
    }

    // جلب الخدمات ذات الصلة (نفس التصنيف)
    const relatedServices = services
      .filter(s => 
        s.category === service.category && 
        s.id !== service.id && 
        s.status === 'published'
      )
      .slice(0, 3);

    return res.status(200).json({
      success: true,
      service,
      relatedServices
    });
  } catch (error) {
    console.error('Error fetching service:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الخدمة'
    });
  }
}
