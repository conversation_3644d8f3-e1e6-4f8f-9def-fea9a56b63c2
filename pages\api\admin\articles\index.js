import { connectToDatabase } from '../../../../lib/mongodb.js';
import { Article } from '../../../../lib/models/index.js';
import { requireAdmin, errorHandler } from '../../../../lib/middleware.js';

const handler = async (req, res) => {
  switch (req.method) {
    case 'GET':
      return getArticles(req, res);
    case 'POST':
      return createArticle(req, res);
    case 'PUT':
      return updateArticle(req, res);
    case 'DELETE':
      return deleteArticle(req, res);
    default:
      return res.status(405).json({
        success: false,
        message: 'Method not allowed'
      });
  }
};

// الحصول على جميع المقالات
const getArticles = async (req, res) => {
  try {
    await connectToDatabase();

    const { page = 1, limit = 10, status, category, search } = req.query;
    const offset = (page - 1) * limit;

    // بناء الفلتر
    let filter = {};

    if (status) {
      filter.status = status;
    }

    if (category) {
      filter.category = category;
    }

    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } },
        { excerpt: { $regex: search, $options: 'i' } }
      ];
    }

    // الحصول على العدد الإجمالي
    const total = await Article.countDocuments(filter);
    const pages = Math.ceil(total / limit);

    // الحصول على المقالات مع التصفح
    const articles = await Article.find(filter)
      .sort({ created_at: -1 })
      .skip(offset)
      .limit(parseInt(limit))
      .lean();

    return res.status(200).json({
      success: true,
      articles,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages
      }
    });
  } catch (error) {
    console.error('Error fetching articles:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب المقالات'
    });
  }
};

// إنشاء مقال جديد
const createArticle = async (req, res) => {
  const {
    title,
    slug,
    excerpt,
    content,
    image,
    author,
    category,
    tags,
    status = 'published',
    featured = false
  } = req.body;

  if (!title || !content) {
    return res.status(400).json({
      success: false,
      message: 'العنوان والمحتوى مطلوبان'
    });
  }

  // إنشاء slug تلقائياً إذا لم يتم توفيره
  const finalSlug = slug || title.toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-');

  try {
    await connectToDatabase();

    const newArticle = new Article({
      title,
      slug: finalSlug,
      excerpt: excerpt || '',
      content,
      image: image || '',
      author: author || 'المدير',
      category: category || '',
      tags: tags || '',
      status,
      featured: Boolean(featured)
    });

    const savedArticle = await newArticle.save();

    return res.status(201).json({
      success: true,
      message: 'تم إنشاء المقال بنجاح',
      article: savedArticle
    });
  } catch (error) {
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'الرابط المختصر موجود مسبقاً'
      });
    }
    console.error('Error creating article:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في إنشاء المقال'
    });
  }
};

// تحديث مقال
const updateArticle = async (req, res) => {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({
      success: false,
      message: 'معرف المقال مطلوب'
    });
  }

  try {
    await connectToDatabase();

    // التحقق من وجود المقال
    const existingArticle = await Article.findById(id);
    if (!existingArticle) {
      return res.status(404).json({
        success: false,
        message: 'المقال غير موجود'
      });
    }

    // الحصول على البيانات المرسلة
    const updateData = req.body;
    const allowedFields = ['title', 'slug', 'excerpt', 'content', 'image', 'author', 'category', 'tags', 'status', 'featured'];

    // بناء بيانات التحديث
    const updateFields = {};
    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key)) {
        updateFields[key] = key === 'featured' ? Boolean(value) : value;
      }
    }

    if (Object.keys(updateFields).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'لا توجد بيانات للتحديث'
      });
    }

    // تحديث المقال
    const updatedArticle = await Article.findByIdAndUpdate(
      id,
      updateFields,
      { new: true, runValidators: true }
    );

    return res.status(200).json({
      success: true,
      message: 'تم تحديث المقال بنجاح',
      article: updatedArticle
    });
  } catch (error) {
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'الرابط المختصر موجود مسبقاً'
      });
    }
    console.error('Error updating article:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في تحديث المقال'
    });
  }
};

// حذف مقال
const deleteArticle = async (req, res) => {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({
      success: false,
      message: 'معرف المقال مطلوب'
    });
  }

  try {
    await connectToDatabase();

    // التحقق من وجود المقال وحذفه
    const deletedArticle = await Article.findByIdAndDelete(id);

    if (!deletedArticle) {
      return res.status(404).json({
        success: false,
        message: 'المقال غير موجود'
      });
    }

    return res.status(200).json({
      success: true,
      message: 'تم حذف المقال بنجاح'
    });
  } catch (error) {
    console.error('Error deleting article:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في حذف المقال'
    });
  }
};

export default requireAdmin(errorHandler(handler));
