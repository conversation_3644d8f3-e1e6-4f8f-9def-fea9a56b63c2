import mongoose from 'mongoose';

const settingSchema = new mongoose.Schema({
  key: {
    type: String,
    required: [true, 'مفتاح الإعداد مطلوب'],
    unique: true,
    trim: true,
    maxlength: [100, 'مفتاح الإعداد يجب أن يكون أقل من 100 حرف']
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: [true, 'قيمة الإعداد مطلوبة']
  },
  type: {
    type: String,
    enum: ['string', 'number', 'boolean', 'object', 'array'],
    required: true
  },
  category: {
    type: String,
    required: [true, 'فئة الإعداد مطلوبة'],
    enum: [
      'general',
      'contact',
      'social',
      'seo',
      'appearance',
      'email',
      'security',
      'analytics',
      'maintenance',
      'legal'
    ]
  },
  label: {
    type: String,
    required: [true, 'تسمية الإعداد مطلوبة'],
    trim: true,
    maxlength: [200, 'التسمية يجب أن تكون أقل من 200 حرف']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'الوصف يجب أن يكون أقل من 500 حرف']
  },
  isPublic: {
    type: Boolean,
    default: false
  },
  isEditable: {
    type: Boolean,
    default: true
  },
  validation: {
    required: {
      type: Boolean,
      default: false
    },
    min: {
      type: Number
    },
    max: {
      type: Number
    },
    pattern: {
      type: String
    },
    options: [{
      label: String,
      value: mongoose.Schema.Types.Mixed
    }]
  },
  order: {
    type: Number,
    default: 0
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
});

// فهرسة للبحث والأداء
settingSchema.index({ key: 1 }, { unique: true });
settingSchema.index({ category: 1 });
settingSchema.index({ isPublic: 1 });
settingSchema.index({ order: 1 });

// طرق النموذج
settingSchema.methods.updateValue = function(newValue) {
  // التحقق من صحة القيمة حسب النوع
  if (this.type === 'number' && isNaN(Number(newValue))) {
    throw new Error('القيمة يجب أن تكون رقم');
  }
  
  if (this.type === 'boolean' && typeof newValue !== 'boolean') {
    throw new Error('القيمة يجب أن تكون true أو false');
  }
  
  // التحقق من الحد الأدنى والأقصى للأرقام
  if (this.type === 'number') {
    const numValue = Number(newValue);
    if (this.validation.min !== undefined && numValue < this.validation.min) {
      throw new Error(`القيمة يجب أن تكون أكبر من أو تساوي ${this.validation.min}`);
    }
    if (this.validation.max !== undefined && numValue > this.validation.max) {
      throw new Error(`القيمة يجب أن تكون أقل من أو تساوي ${this.validation.max}`);
    }
  }
  
  // التحقق من النمط للنصوص
  if (this.type === 'string' && this.validation.pattern) {
    const regex = new RegExp(this.validation.pattern);
    if (!regex.test(newValue)) {
      throw new Error('القيمة لا تتطابق مع النمط المطلوب');
    }
  }
  
  this.value = newValue;
  return this.save();
};

// طرق ثابتة
settingSchema.statics.getValue = async function(key, defaultValue = null) {
  const setting = await this.findOne({ key });
  return setting ? setting.value : defaultValue;
};

settingSchema.statics.setValue = async function(key, value) {
  const setting = await this.findOne({ key });
  if (setting) {
    return setting.updateValue(value);
  } else {
    throw new Error(`الإعداد ${key} غير موجود`);
  }
};

settingSchema.statics.getByCategory = function(category) {
  return this.find({ category }).sort({ order: 1, label: 1 });
};

settingSchema.statics.getPublicSettings = function() {
  return this.find({ isPublic: true }).sort({ category: 1, order: 1 });
};

settingSchema.statics.getEditableSettings = function() {
  return this.find({ isEditable: true }).sort({ category: 1, order: 1 });
};

settingSchema.statics.initializeDefaults = async function() {
  const defaultSettings = [
    // إعدادات عامة
    {
      key: 'site_name',
      value: 'مكتب المحاماة',
      type: 'string',
      category: 'general',
      label: 'اسم الموقع',
      description: 'اسم الموقع الذي يظهر في العنوان',
      isPublic: true,
      isEditable: true
    },
    {
      key: 'site_description',
      value: 'مكتب محاماة متخصص في الخدمات القانونية',
      type: 'string',
      category: 'general',
      label: 'وصف الموقع',
      description: 'وصف مختصر للموقع',
      isPublic: true,
      isEditable: true
    },
    {
      key: 'site_logo',
      value: '/images/logo.png',
      type: 'string',
      category: 'appearance',
      label: 'شعار الموقع',
      description: 'رابط شعار الموقع',
      isPublic: true,
      isEditable: true
    },
    
    // معلومات التواصل
    {
      key: 'contact_email',
      value: '<EMAIL>',
      type: 'string',
      category: 'contact',
      label: 'البريد الإلكتروني',
      description: 'البريد الإلكتروني الرئيسي للمكتب',
      isPublic: true,
      isEditable: true,
      validation: {
        pattern: '^[\\w\\.-]+@[\\w\\.-]+\\.[a-zA-Z]{2,}$'
      }
    },
    {
      key: 'contact_phone',
      value: '+966123456789',
      type: 'string',
      category: 'contact',
      label: 'رقم الهاتف',
      description: 'رقم الهاتف الرئيسي للمكتب',
      isPublic: true,
      isEditable: true
    },
    {
      key: 'contact_address',
      value: 'الرياض، المملكة العربية السعودية',
      type: 'string',
      category: 'contact',
      label: 'العنوان',
      description: 'عنوان المكتب',
      isPublic: true,
      isEditable: true
    },
    
    // وسائل التواصل الاجتماعي
    {
      key: 'social_facebook',
      value: '',
      type: 'string',
      category: 'social',
      label: 'فيسبوك',
      description: 'رابط صفحة فيسبوك',
      isPublic: true,
      isEditable: true
    },
    {
      key: 'social_twitter',
      value: '',
      type: 'string',
      category: 'social',
      label: 'تويتر',
      description: 'رابط حساب تويتر',
      isPublic: true,
      isEditable: true
    },
    {
      key: 'social_linkedin',
      value: '',
      type: 'string',
      category: 'social',
      label: 'لينكد إن',
      description: 'رابط صفحة لينكد إن',
      isPublic: true,
      isEditable: true
    },
    
    // إعدادات SEO
    {
      key: 'seo_meta_title',
      value: 'مكتب المحاماة - خدمات قانونية متميزة',
      type: 'string',
      category: 'seo',
      label: 'عنوان SEO',
      description: 'العنوان الذي يظهر في نتائج البحث',
      isPublic: true,
      isEditable: true
    },
    {
      key: 'seo_meta_description',
      value: 'مكتب محاماة متخصص في تقديم الخدمات القانونية والاستشارات القانونية',
      type: 'string',
      category: 'seo',
      label: 'وصف SEO',
      description: 'الوصف الذي يظهر في نتائج البحث',
      isPublic: true,
      isEditable: true
    }
  ];
  
  for (const setting of defaultSettings) {
    const exists = await this.findOne({ key: setting.key });
    if (!exists) {
      await this.create(setting);
    }
  }
  
  console.log('Default settings initialized');
};

// تصدير النموذج
const Setting = mongoose.models.Setting || mongoose.model('Setting', settingSchema);

export default Setting;
