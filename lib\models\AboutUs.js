import mongoose from 'mongoose';

const aboutUsSchema = new mongoose.Schema({
  // المعلومات الأساسية
  title: {
    type: String,
    required: [true, 'عنوان الصفحة مطلوب'],
    trim: true,
    default: 'من نحن'
  },
  subtitle: {
    type: String,
    trim: true,
    maxlength: [200, 'العنوان الفرعي يجب أن يكون أقل من 200 حرف']
  },
  description: {
    type: String,
    required: [true, 'الوصف مطلوب'],
    trim: true
  },
  
  // قسم الرؤية والرسالة والقيم
  vision: {
    title: {
      type: String,
      default: 'رؤيتنا',
      trim: true
    },
    content: {
      type: String,
      trim: true
    },
    icon: {
      type: String,
      trim: true
    }
  },
  mission: {
    title: {
      type: String,
      default: 'رسالتنا',
      trim: true
    },
    content: {
      type: String,
      trim: true
    },
    icon: {
      type: String,
      trim: true
    }
  },
  values: {
    title: {
      type: String,
      default: 'قيمنا',
      trim: true
    },
    content: {
      type: String,
      trim: true
    },
    icon: {
      type: String,
      trim: true
    }
  },

  // قسم تاريخ المكتب
  history: {
    title: {
      type: String,
      default: 'تاريخنا',
      trim: true
    },
    content: {
      type: String,
      trim: true
    },
    foundedYear: {
      type: Number,
      min: 1900,
      max: new Date().getFullYear()
    },
    milestones: [{
      year: {
        type: Number,
        required: true
      },
      title: {
        type: String,
        required: true,
        trim: true
      },
      description: {
        type: String,
        trim: true
      }
    }]
  },

  // الإحصائيات
  statistics: [{
    title: {
      type: String,
      required: true,
      trim: true
    },
    value: {
      type: String,
      required: true,
      trim: true
    },
    suffix: {
      type: String,
      trim: true,
      default: ''
    },
    icon: {
      type: String,
      trim: true
    },
    order: {
      type: Number,
      default: 0
    }
  }],

  // الصور
  images: {
    hero: {
      type: String,
      trim: true
    },
    about: {
      type: String,
      trim: true
    },
    team: {
      type: String,
      trim: true
    },
    office: [{
      url: {
        type: String,
        required: true,
        trim: true
      },
      caption: {
        type: String,
        trim: true
      },
      order: {
        type: Number,
        default: 0
      }
    }]
  },

  // معلومات الاتصال المخصصة
  contact: {
    address: {
      type: String,
      trim: true
    },
    phone: {
      type: String,
      trim: true
    },
    email: {
      type: String,
      trim: true,
      lowercase: true
    },
    workingHours: {
      type: String,
      trim: true
    },
    socialMedia: {
      facebook: {
        type: String,
        trim: true
      },
      twitter: {
        type: String,
        trim: true
      },
      linkedin: {
        type: String,
        trim: true
      },
      instagram: {
        type: String,
        trim: true
      }
    }
  },

  // إعدادات SEO
  seo: {
    metaTitle: {
      type: String,
      trim: true,
      maxlength: [60, 'عنوان SEO يجب أن يكون أقل من 60 حرف']
    },
    metaDescription: {
      type: String,
      trim: true,
      maxlength: [160, 'وصف SEO يجب أن يكون أقل من 160 حرف']
    },
    keywords: {
      type: String,
      trim: true
    }
  },

  // الحالة والإعدادات
  status: {
    type: String,
    enum: ['draft', 'published'],
    default: 'published'
  },
  showInMenu: {
    type: Boolean,
    default: true
  },
  menuOrder: {
    type: Number,
    default: 2
  },

  // معلومات التحديث
  lastUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
});

// فهرسة
aboutUsSchema.index({ status: 1 });
aboutUsSchema.index({ showInMenu: 1 });
aboutUsSchema.index({ menuOrder: 1 });

// طرق النموذج
aboutUsSchema.methods.getStatisticByTitle = function(title) {
  return this.statistics.find(stat => stat.title === title);
};

aboutUsSchema.methods.addStatistic = function(statistic) {
  this.statistics.push(statistic);
  return this.save();
};

aboutUsSchema.methods.updateStatistic = function(index, statistic) {
  if (this.statistics[index]) {
    this.statistics[index] = { ...this.statistics[index], ...statistic };
    return this.save();
  }
  return Promise.reject(new Error('Statistic not found'));
};

aboutUsSchema.methods.removeStatistic = function(index) {
  if (this.statistics[index]) {
    this.statistics.splice(index, 1);
    return this.save();
  }
  return Promise.reject(new Error('Statistic not found'));
};

aboutUsSchema.methods.addMilestone = function(milestone) {
  this.history.milestones.push(milestone);
  // ترتيب المعالم حسب السنة
  this.history.milestones.sort((a, b) => a.year - b.year);
  return this.save();
};

aboutUsSchema.methods.addOfficeImage = function(image) {
  this.images.office.push(image);
  // ترتيب الصور حسب الترتيب
  this.images.office.sort((a, b) => a.order - b.order);
  return this.save();
};

// طرق ثابتة
aboutUsSchema.statics.getPublished = function() {
  return this.findOne({ status: 'published' });
};

aboutUsSchema.statics.getForMenu = function() {
  return this.findOne({ 
    status: 'published', 
    showInMenu: true 
  }).select('title menuOrder');
};

// تصدير النموذج
const AboutUs = mongoose.models.AboutUs || mongoose.model('AboutUs', aboutUsSchema);

export default AboutUs;
