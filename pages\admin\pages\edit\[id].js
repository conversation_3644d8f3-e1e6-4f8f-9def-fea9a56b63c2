import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '../../../../components/admin/AdminLayout';
import SimpleRichTextEditor from '../../../../components/admin/SimpleRichTextEditor';
import Link from 'next/link';
import { 
  FaSave, 
  FaArrowLeft, 
  FaEye, 
  FaImage, 
  FaCopy,
  FaExclamationTriangle,
  FaCheckCircle
} from 'react-icons/fa';

export default function EditPage() {
  const router = useRouter();
  const { id } = router.query;
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [validationErrors, setValidationErrors] = useState({});
  
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    content: '',
    meta_title: '',
    meta_description: '',
    status: 'published',
    template: 'default',
    featured_image: ''
  });

  const templates = [
    { value: 'default', label: 'افتراضي' },
    { value: 'contact', label: 'اتصل بنا' },
    { value: 'about', label: 'من نحن' },
    { value: 'services', label: 'الخدمات' },
    { value: 'custom', label: 'مخصص' }
  ];

  useEffect(() => {
    if (id) {
      fetchPage();
    }
  }, [id]);

  const fetchPage = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/pages/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      
      if (data.success) {
        setFormData(data.page);
      } else {
        setError(data.message);
      }
    } catch (error) {
      setError('حدث خطأ في جلب بيانات الصفحة');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // مسح رسالة الخطأ عند التعديل
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleContentChange = (content) => {
    setFormData(prev => ({
      ...prev,
      content
    }));

    if (validationErrors.content) {
      setValidationErrors(prev => ({
        ...prev,
        content: ''
      }));
    }
  };

  const generateSlug = (title) => {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim();
  };

  const handleTitleChange = (e) => {
    const title = e.target.value;
    setFormData(prev => ({
      ...prev,
      title,
      slug: generateSlug(title)
    }));

    if (validationErrors.title) {
      setValidationErrors(prev => ({
        ...prev,
        title: ''
      }));
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.title.trim()) {
      errors.title = 'عنوان الصفحة مطلوب';
    }

    if (!formData.content.trim()) {
      errors.content = 'محتوى الصفحة مطلوب';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      setError('يرجى تصحيح الأخطاء المذكورة أعلاه');
      return;
    }

    setSaving(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/pages/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('تم تحديث الصفحة بنجاح');
        setTimeout(() => {
          router.push('/admin/pages');
        }, 2000);
      } else {
        setError(data.message || 'حدث خطأ في تحديث الصفحة');
      }
    } catch (error) {
      setError('حدث خطأ في الاتصال بالخادم');
    } finally {
      setSaving(false);
    }
  };

  const handleDuplicate = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/pages/${id}/duplicate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      
      if (data.success) {
        setSuccess('تم نسخ الصفحة بنجاح');
        setTimeout(() => {
          router.push(`/admin/pages/edit/${data.page.id}`);
        }, 2000);
      } else {
        setError(data.message);
      }
    } catch (error) {
      setError('حدث خطأ في نسخ الصفحة');
    }
  };

  if (loading) {
    return (
      <AdminLayout title="تعديل الصفحة">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-900 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري التحميل...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="تعديل الصفحة">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">تعديل الصفحة</h1>
            <p className="text-gray-600">تحديث بيانات ومحتوى الصفحة</p>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={handleDuplicate}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <FaCopy />
              نسخ الصفحة
            </button>
            <Link href="/admin/pages">
              <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                <FaArrowLeft />
                العودة إلى الصفحات
              </button>
            </Link>
          </div>
        </div>

        {/* Messages */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded flex items-center">
            <FaExclamationTriangle className="ml-2" />
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded flex items-center">
            <FaCheckCircle className="ml-2" />
            {success}
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Basic Info */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">معلومات الصفحة الأساسية</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      عنوان الصفحة *
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleTitleChange}
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                        validationErrors.title ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="أدخل عنوان الصفحة..."
                    />
                    {validationErrors.title && (
                      <p className="mt-2 text-sm text-red-600 flex items-center">
                        <FaExclamationTriangle className="ml-1" />
                        {validationErrors.title}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الرابط المختصر (Slug)
                    </label>
                    <input
                      type="text"
                      name="slug"
                      value={formData.slug}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="page-slug"
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      سيتم إنشاؤه تلقائياً من العنوان إذا تُرك فارغاً
                    </p>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">محتوى الصفحة</h2>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    محتوى الصفحة *
                  </label>
                  <div className={`rounded-xl overflow-hidden ${
                    validationErrors.content
                      ? 'ring-2 ring-red-500'
                      : formData.content
                      ? 'ring-2 ring-green-500'
                      : ''
                  }`}>
                    <SimpleRichTextEditor
                      value={formData.content}
                      onChange={handleContentChange}
                      placeholder="ابدأ في كتابة محتوى الصفحة..."
                      height="500px"
                      showWordCount={true}
                    />
                  </div>
                  {validationErrors.content && (
                    <p className="mt-2 text-sm text-red-600 flex items-center">
                      <FaExclamationTriangle className="ml-1" />
                      {validationErrors.content}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Publish Settings */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">إعدادات النشر</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      حالة الصفحة
                    </label>
                    <select
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="published">منشور</option>
                      <option value="draft">مسودة</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      قالب الصفحة
                    </label>
                    <select
                      name="template"
                      value={formData.template}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      {templates.map((template) => (
                        <option key={template.value} value={template.value}>
                          {template.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>

              {/* SEO Settings */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">إعدادات SEO</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      عنوان SEO
                    </label>
                    <input
                      type="text"
                      name="meta_title"
                      value={formData.meta_title}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="عنوان الصفحة في محركات البحث"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      وصف SEO
                    </label>
                    <textarea
                      name="meta_description"
                      value={formData.meta_description}
                      onChange={handleInputChange}
                      rows={3}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="وصف الصفحة في محركات البحث"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <FaImage className="inline ml-1" />
                      صورة مميزة
                    </label>
                    <input
                      type="url"
                      name="featured_image"
                      value={formData.featured_image}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="https://example.com/image.jpg"
                    />
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="bg-white rounded-lg shadow p-6">
                <div className="space-y-3">
                  <button
                    type="submit"
                    disabled={saving}
                    className="w-full bg-primary-900 text-white px-6 py-3 rounded-lg hover:bg-primary-800 transition-colors duration-200 flex items-center justify-center disabled:opacity-50"
                  >
                    {saving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                        جاري الحفظ...
                      </>
                    ) : (
                      <>
                        <FaSave className="ml-2" />
                        تحديث الصفحة
                      </>
                    )}
                  </button>

                  {formData.status === 'published' && (
                    <Link href={`/${formData.slug}`} target="_blank">
                      <button
                        type="button"
                        className="w-full bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center justify-center"
                      >
                        <FaEye className="ml-2" />
                        معاينة الصفحة
                      </button>
                    </Link>
                  )}
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
}
