import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import LocalizedLayout from '../../../components/LocalizedLayout';
import { useTranslation } from '../../../hooks/useTranslation';
import { isValidLocale, defaultLocale } from '../../../lib/i18n';
import { 
  FaGavel, 
  FaBuilding, 
  FaHandshake, 
  FaLightbulb, 
  FaHome, 
  FaGlobeAmericas,
  FaFileContract,
  FaUsers,
  FaArrowLeft,
  FaCheck,
  FaStar
} from 'react-icons/fa';

export default function ServicesPage() {
  const router = useRouter();
  const { locale } = router.query;
  const { t } = useTranslation();

  // التحقق من صحة اللغة
  useEffect(() => {
    if (locale && !isValidLocale(locale)) {
      router.replace(`/${defaultLocale}/services`);
    }
  }, [locale, router]);

  const services = [
    {
      id: 'judicial-disputes',
      icon: FaGavel,
      title: t('services.judicialDisputes'),
      description: 'تمثيل قانوني متميز أمام جميع المحاكم ومراكز التحكيم المحلية والدولية',
      features: [
        'التقاضي المدني والتجاري',
        'التحكيم الدولي والمحلي',
        'الوساطة وحل النزاعات',
        'تنفيذ الأحكام والقرارات',
        'الطعون والاستئنافات',
        'القضايا الإدارية'
      ],
      color: 'bg-blue-500',
      href: `/${locale}/services/judicial-disputes`
    },
    {
      id: 'corporate-and-investment',
      icon: FaBuilding,
      title: t('services.corporateAndInvestment'),
      description: 'خدمات قانونية شاملة لتأسيس وإدارة الشركات والاستثمارات',
      features: [
        'تأسيس الشركات بجميع أنواعها',
        'صياغة العقود التجارية',
        'الاندماج والاستحواذ',
        'الامتثال التنظيمي',
        'حوكمة الشركات',
        'الاستثمار الأجنبي'
      ],
      color: 'bg-green-500',
      href: `/${locale}/services/corporate-and-investment`
    },
    {
      id: 'arbitration-and-dispute-settlement',
      icon: FaHandshake,
      title: t('services.arbitrationAndDispute'),
      description: 'حلول بديلة لفض النزاعات خارج المحاكم بطرق سريعة وفعالة',
      features: [
        'التحكيم التجاري',
        'الوساطة القانونية',
        'التفاوض المتقدم',
        'تسوية النزاعات',
        'التحكيم الدولي',
        'الخبرة التحكيمية'
      ],
      color: 'bg-purple-500',
      href: `/${locale}/services/arbitration-and-dispute-settlement`
    },
    {
      id: 'legal-advice',
      icon: FaLightbulb,
      title: t('services.legalAdvice'),
      description: 'استشارات قانونية متخصصة في جميع فروع القانون',
      features: [
        'الاستشارات الفورية',
        'الدراسات القانونية',
        'المراجعة القانونية',
        'التدريب القانوني',
        'الرأي القانوني',
        'التحليل القانوني'
      ],
      color: 'bg-yellow-500',
      href: `/${locale}/services/legal-advice`
    },
    {
      id: 'property-sector',
      icon: FaHome,
      title: t('services.propertySector'),
      description: 'استشارات ومعاملات عقارية متكاملة للأفراد والمطورين',
      features: [
        'شراء وبيع العقارات',
        'تطوير المشاريع العقارية',
        'عقود الإيجار والتمليك',
        'تسوية المنازعات العقارية',
        'التخطيط العمراني',
        'التمويل العقاري'
      ],
      color: 'bg-red-500',
      href: `/${locale}/services/property-sector`
    },
    {
      id: 'foreigners-and-investors-affairs',
      icon: FaGlobeAmericas,
      title: t('services.foreignersAndInvestors'),
      description: 'خدمات قانونية متخصصة للمستثمرين الأجانب والشركات الدولية',
      features: [
        'تأشيرات الاستثمار',
        'الإقامة والجنسية',
        'التراخيص التجارية',
        'الامتثال الضريبي',
        'الاستثمار الأجنبي',
        'الشراكات الدولية'
      ],
      color: 'bg-indigo-500',
      href: `/${locale}/services/foreigners-and-investors-affairs`
    },
    {
      id: 'government-regulations-and-licensing',
      icon: FaUsers,
      title: t('services.governmentRegulations'),
      description: 'إدارة العلاقات الحكومية والحصول على التراخيص المطلوبة',
      features: [
        'التراخيص الحكومية',
        'العلاقات الحكومية',
        'الامتثال التنظيمي',
        'المناقصات الحكومية',
        'التفتيش والرقابة',
        'السياسات العامة'
      ],
      color: 'bg-teal-500',
      href: `/${locale}/services/government-regulations-and-licensing`
    },
    {
      id: 'contracts-sector',
      icon: FaFileContract,
      title: t('services.contractsSector'),
      description: 'صياغة ومراجعة العقود بجميع أنواعها بأعلى معايير الجودة',
      features: [
        'عقود الشركات',
        'العقود التجارية',
        'عقود العمل',
        'العقود الدولية',
        'عقود التوريد',
        'عقود الخدمات'
      ],
      color: 'bg-orange-500',
      href: `/${locale}/services/contracts-sector`
    }
  ];

  const whyChooseUs = [
    {
      icon: FaStar,
      title: 'خبرة متميزة',
      description: 'أكثر من 20 عاماً من الخبرة في مختلف فروع القانون'
    },
    {
      icon: FaUsers,
      title: 'فريق متخصص',
      description: 'محامون متخصصون في كل مجال قانوني'
    },
    {
      icon: FaCheck,
      title: 'نتائج مضمونة',
      description: 'سجل حافل من النجاحات والقضايا المكسوبة'
    },
    {
      icon: FaGlobeAmericas,
      title: 'تغطية شاملة',
      description: 'خدمات قانونية محلية ودولية'
    }
  ];

  if (!locale || !isValidLocale(locale)) {
    return null;
  }

  return (
    <LocalizedLayout 
      locale={locale}
      title={`${t('services.title')} - مكتب المحاماة`}
      description={t('services.subtitle')}
    >
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-900 to-primary-700 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {t('services.title')}
            </h1>
            <p className="text-xl text-primary-100 leading-relaxed">
              {t('services.subtitle')}
            </p>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {services.map((service) => (
              <div
                key={service.id}
                className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group border border-gray-100"
              >
                <div className={`${service.color} p-6 text-white`}>
                  <service.icon className="text-4xl mb-4" />
                  <h3 className="text-xl font-bold mb-2">{service.title}</h3>
                </div>
                
                <div className="p-6">
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {service.description}
                  </p>
                  
                  <ul className="space-y-2 mb-6">
                    {service.features.slice(0, 4).map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-700">
                        <FaCheck className="text-green-500 ml-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Link href={service.href}>
                    <button className="w-full bg-primary-600 text-white py-3 px-4 rounded-lg hover:bg-primary-700 transition-colors duration-200 flex items-center justify-center gap-2 group">
                      {t('common.readMore')}
                      <FaArrowLeft className="group-hover:translate-x-1 transition-transform" />
                    </button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-primary-900 mb-4">
              لماذا تختار خدماتنا؟
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              نقدم خدمات قانونية متميزة تجمع بين الخبرة والاحترافية والنتائج المضمونة
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {whyChooseUs.map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <item.icon className="text-2xl text-primary-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{item.title}</h3>
                <p className="text-gray-600">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How We Work */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-primary-900 mb-4">
              كيف نعمل معك؟
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              عملية واضحة ومنظمة لضمان تقديم أفضل الخدمات القانونية
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                1
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">الاستشارة الأولية</h3>
              <p className="text-gray-600">
                نستمع لقضيتك ونقيم الوضع القانوني ونقدم الاستشارة المبدئية
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-20 h-20 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                2
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">وضع الاستراتيجية</h3>
              <p className="text-gray-600">
                نضع خطة عمل مفصلة وواضحة لتحقيق أفضل النتائج الممكنة
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-20 h-20 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                3
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">التنفيذ والمتابعة</h3>
              <p className="text-gray-600">
                ننفذ الخطة بدقة ونتابع التطورات حتى الوصول للنتيجة المطلوبة
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            هل تحتاج استشارة قانونية متخصصة؟
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            تواصل معنا الآن للحصول على استشارة مجانية من فريق المحامين المتخصصين
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href={`/${locale}/contact-us`}>
              <button className="btn-accent">
                {t('nav.freeConsultation')}
              </button>
            </Link>
            <Link href={`/${locale}/our-team`}>
              <button className="btn-outline-white">
                {t('nav.ourTeam')}
              </button>
            </Link>
          </div>
        </div>
      </section>
    </LocalizedLayout>
  );
}
