import { getUserFromToken } from './auth';

// Middleware للتحقق من المصادقة
export const requireAuth = (handler) => {
  return async (req, res) => {
    try {
      const token = req.headers.authorization?.replace('Bearer ', '') || 
                   req.cookies?.token;

      if (!token) {
        return res.status(401).json({ 
          success: false, 
          message: 'غير مصرح لك بالوصول' 
        });
      }

      const user = await getUserFromToken(token);
      
      if (!user) {
        return res.status(401).json({ 
          success: false, 
          message: 'التوكن غير صالح' 
        });
      }

      req.user = user;
      return handler(req, res);
    } catch (error) {
      return res.status(500).json({ 
        success: false, 
        message: 'خطأ في الخادم' 
      });
    }
  };
};

// Middleware للتحقق من صلاحيات الإدارة
export const requireAdmin = (handler) => {
  return requireAuth(async (req, res) => {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ 
        success: false, 
        message: 'غير مصرح لك بهذا الإجراء' 
      });
    }
    
    return handler(req, res);
  });
};

// معالج الأخطاء
export const errorHandler = (handler) => {
  return async (req, res) => {
    try {
      return await handler(req, res);
    } catch (error) {
      console.error('API Error:', error);
      return res.status(500).json({
        success: false,
        message: 'حدث خطأ في الخادم',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };
};
