import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Layout from '../../components/LocalizedLayout';
import Hero from '../../components/Hero';
import ServiceCard from '../../components/ServiceCard';
import TeamMember from '../../components/TeamMember';
import { useTranslation } from '../../hooks/useTranslation';
import { isValidLocale, defaultLocale } from '../../lib/i18n';
import Link from 'next/link';
import { 
  FaBuilding, 
  FaGavel, 
  FaHome, 
  FaUsers, 
  FaLightbulb, 
  FaCalculator,
  FaShieldAlt,
  FaHandshake,
  FaAward,
  FaGlobeAmericas,
  FaArrowLeft,
  FaQuoteLeft
} from 'react-icons/fa';

export default function Home() {
  const router = useRouter();
  const { locale } = router.query;
  const { t, dir } = useTranslation();

  // التحقق من صحة اللغة
  useEffect(() => {
    if (locale && !isValidLocale(locale)) {
      router.replace(`/${defaultLocale}`);
    }
  }, [locale, router]);

  const services = [
    {
      icon: FaBuilding,
      title: t('services.corporateAndInvestment'),
      description: 'خدمات قانونية شاملة لتأسيس وإدارة الشركات والمؤسسات التجارية',
      features: [
        'تأسيس الشركات بجميع أنواعها',
        'صياغة العقود التجارية',
        'الاندماج والاستحواذ',
        'الامتثال التنظيمي'
      ]
    },
    {
      icon: FaGavel,
      title: t('services.judicialDisputes'),
      description: 'تمثيل قانوني متميز أمام جميع المحاكم ومراكز التحكيم',
      features: [
        'التقاضي المدني والتجاري',
        'التحكيم الدولي والمحلي',
        'الوساطة وحل النزاعات',
        'تنفيذ الأحكام والقرارات'
      ]
    },
    {
      icon: FaHome,
      title: t('services.propertySector'),
      description: 'استشارات ومعاملات عقارية متكاملة للأفراد والمطورين',
      features: [
        'شراء وبيع العقارات',
        'تطوير المشاريع العقارية',
        'عقود الإيجار والتمليك',
        'تسوية المنازعات العقارية'
      ]
    },
    {
      icon: FaUsers,
      title: t('services.legalAdvice'),
      description: 'استشارات قانونية متخصصة في جميع فروع القانون',
      features: [
        'الاستشارات الفورية',
        'الدراسات القانونية',
        'المراجعة القانونية',
        'التدريب القانوني'
      ]
    },
    {
      icon: FaLightbulb,
      title: t('services.arbitrationAndDispute'),
      description: 'حلول بديلة لفض النزاعات خارج المحاكم',
      features: [
        'التحكيم التجاري',
        'الوساطة القانونية',
        'التفاوض المتقدم',
        'تسوية النزاعات'
      ]
    },
    {
      icon: FaCalculator,
      title: t('services.contractsSector'),
      description: 'صياغة ومراجعة العقود بجميع أنواعها',
      features: [
        'عقود الشركات',
        'العقود التجارية',
        'عقود العمل',
        'العقود الدولية'
      ]
    }
  ];

  const stats = [
    { number: '500+', label: 'عميل راضٍ', icon: FaUsers },
    { number: '1000+', label: 'قضية ناجحة', icon: FaGavel },
    { number: '20+', label: 'سنة خبرة', icon: FaAward },
    { number: '50+', label: 'محامي متخصص', icon: FaShieldAlt }
  ];

  const teamMembers = [
    {
      name: 'د. أحمد محمد العلي',
      position: 'الشريك المؤسس والمدير التنفيذي',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      specializations: ['قانون الشركات', 'التحكيم التجاري']
    },
    {
      name: 'أ. فاطمة أحمد الزهراني',
      position: 'شريك أول - قانون الأسرة',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      specializations: ['قانون الأسرة', 'الأحوال الشخصية']
    },
    {
      name: 'أ. محمد سالم القحطاني',
      position: 'شريك - القانون العقاري',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
      specializations: ['القانون العقاري', 'قانون البناء']
    }
  ];

  const testimonials = [
    {
      name: 'م. سارة أحمد',
      company: 'شركة التقنية المتقدمة',
      text: 'تعاملنا مع المكتب في عدة قضايا قانونية معقدة، وكانت النتائج مذهلة. فريق محترف ومتفهم لاحتياجات العمل.',
      rating: 5
    },
    {
      name: 'أ. محمد الغامدي',
      company: 'مجموعة الغامدي التجارية',
      text: 'خدمة متميزة في مجال قانون الشركات. ساعدونا في تأسيس شركتنا وتنظيم جميع الأمور القانونية بكفاءة عالية.',
      rating: 5
    }
  ];

  if (!locale || !isValidLocale(locale)) {
    return null; // أو مكون تحميل
  }

  return (
    <Layout 
      locale={locale}
      title={`${t('nav.home')} - مكتب المحاماة للاستشارات القانونية`}
      description="مكتب محاماة متخصص في تقديم الاستشارات القانونية والخدمات القانونية المتكاملة للأفراد والشركات بأعلى معايير الجودة والاحترافية"
    >
      {/* Hero Section */}
      <Hero locale={locale} />

      {/* About Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-primary-900 mb-6">
                {t('nav.aboutUs')}
              </h2>
              <p className="text-gray-600 text-lg leading-relaxed mb-6">
                مكتب محاماة رائد في المملكة العربية السعودية، نقدم خدمات قانونية متميزة منذ أكثر من 20 عاماً. 
                نحن ملتزمون بتقديم أعلى مستويات الخدمة القانونية لعملائنا من الأفراد والشركات.
              </p>
              <div className="grid grid-cols-2 gap-6 mb-8">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="flex justify-center mb-2">
                      <stat.icon className="text-3xl text-primary-600" />
                    </div>
                    <div className="text-2xl font-bold text-primary-900">{stat.number}</div>
                    <div className="text-gray-600">{stat.label}</div>
                  </div>
                ))}
              </div>
              <Link href={`/${locale}/about-us`}>
                <button className="btn-primary">
                  {t('common.readMore')}
                </button>
              </Link>
            </div>
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1589829545856-d10d557cf95f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="مكتب المحاماة"
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-primary-900 mb-4">
              {t('services.title')}
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              {t('services.subtitle')}
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <ServiceCard key={index} {...service} />
            ))}
          </div>
          <div className="text-center mt-12">
            <Link href={`/${locale}/services`}>
              <button className="btn-secondary">
                {t('common.viewAll')}
              </button>
            </Link>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-primary-900 mb-4">
              {t('nav.ourTeam')}
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              تعرف على فريق المحامين المتخصصين الذين يقدمون خدمات قانونية متميزة
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <TeamMember key={index} {...member} />
            ))}
          </div>
          <div className="text-center mt-12">
            <Link href={`/${locale}/our-team`}>
              <button className="btn-secondary">
                {t('common.viewAll')}
              </button>
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-primary-900 mb-4">
              آراء عملائنا
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              شهادات حقيقية من عملائنا الكرام حول جودة خدماتنا القانونية
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white p-8 rounded-lg shadow-lg">
                <FaQuoteLeft className="text-3xl text-primary-600 mb-4" />
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {testimonial.text}
                </p>
                <div className="flex items-center">
                  <div>
                    <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                    <p className="text-gray-600 text-sm">{testimonial.company}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            هل تحتاج استشارة قانونية؟
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            تواصل معنا الآن للحصول على استشارة قانونية مجانية من فريق المحامين المتخصصين
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href={`/${locale}/contact-us`}>
              <button className="btn-accent">
                {t('nav.freeConsultation')}
              </button>
            </Link>
            <Link href={`/${locale}/services`}>
              <button className="btn-outline-white">
                {t('common.viewAll')}
              </button>
            </Link>
          </div>
        </div>
      </section>
    </Layout>
  );
}
