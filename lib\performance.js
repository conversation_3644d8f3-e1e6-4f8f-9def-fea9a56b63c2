// تحسينات الأداء لـ Next.js 15

// تحسين تحميل الصور
export const imageOptimization = {
  // إعدادات الصور المحسنة
  quality: 85,
  formats: ['image/webp', 'image/avif'],
  
  // أحجام الصور المختلفة
  sizes: {
    thumbnail: '(max-width: 150px) 100vw, 150px',
    small: '(max-width: 300px) 100vw, 300px',
    medium: '(max-width: 600px) 100vw, 600px',
    large: '(max-width: 1200px) 100vw, 1200px',
    hero: '100vw',
  },
  
  // placeholder للصور
  placeholder: 'blur',
  blurDataURL: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
};

// تحسين الخطوط
export const fontOptimization = {
  // تحميل الخطوط بشكل محسن
  preload: [
    {
      href: 'https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvalIkTp2mxdt0.woff2',
      as: 'font',
      type: 'font/woff2',
      crossOrigin: 'anonymous',
    }
  ],
  
  // إعدادات font-display
  display: 'swap',
  
  // تحسين تحميل الخطوط
  fallback: [
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'sans-serif'
  ],
};

// تحسين تحميل JavaScript
export const scriptOptimization = {
  // استراتيجيات تحميل المكتبات الخارجية
  strategies: {
    analytics: 'afterInteractive',
    ads: 'lazyOnload',
    social: 'lazyOnload',
    maps: 'lazyOnload',
  },
  
  // تحسين حجم الحزم
  bundleAnalyzer: process.env.ANALYZE === 'true',
};

// تحسين CSS
export const cssOptimization = {
  // إزالة CSS غير المستخدم
  purgeCSS: {
    content: [
      './pages/**/*.{js,ts,jsx,tsx}',
      './components/**/*.{js,ts,jsx,tsx}',
    ],
    safelist: [
      'prose',
      'prose-lg',
      /^prose-/,
      /^hljs/,
      /^ql-/,
    ],
  },
  
  // ضغط CSS
  minify: true,
  
  // تحسين Critical CSS
  critical: {
    inline: true,
    minify: true,
    extract: true,
  },
};

// تحسين API Routes
export const apiOptimization = {
  // إعدادات التخزين المؤقت
  cache: {
    maxAge: 60, // ثانية
    staleWhileRevalidate: 300, // ثانية
  },
  
  // ضغط الاستجابات
  compression: true,
  
  // تحديد حجم الطلبات
  bodyParser: {
    sizeLimit: '10mb',
  },
};

// مراقبة الأداء
export const performanceMonitoring = {
  // Core Web Vitals
  vitals: {
    LCP: 2.5, // Largest Contentful Paint
    FID: 100, // First Input Delay
    CLS: 0.1, // Cumulative Layout Shift
  },
  
  // مراقبة الأداء
  monitoring: {
    enabled: process.env.NODE_ENV === 'production',
    endpoint: '/api/analytics/performance',
  },
};

// تحسين SEO
export const seoOptimization = {
  // إعدادات الروبوتات
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  
  // إعدادات Open Graph
  openGraph: {
    type: 'website',
    locale: 'ar_SA',
    siteName: 'مكتب المحاماة',
  },
  
  // إعدادات Twitter
  twitter: {
    cardType: 'summary_large_image',
  },
};

// تحسين PWA
export const pwaOptimization = {
  // إعدادات Service Worker
  serviceWorker: {
    enabled: process.env.NODE_ENV === 'production',
    scope: '/',
    updateViaCache: 'none',
  },
  
  // إعدادات التخزين المؤقت
  caching: {
    runtimeCaching: [
      {
        urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
        handler: 'CacheFirst',
        options: {
          cacheName: 'google-fonts-cache',
          expiration: {
            maxEntries: 10,
            maxAgeSeconds: 60 * 60 * 24 * 365, // سنة واحدة
          },
        },
      },
      {
        urlPattern: /\.(?:png|jpg|jpeg|svg|gif|webp|avif)$/i,
        handler: 'StaleWhileRevalidate',
        options: {
          cacheName: 'images-cache',
          expiration: {
            maxEntries: 100,
            maxAgeSeconds: 60 * 60 * 24 * 30, // شهر واحد
          },
        },
      },
    ],
  },
};

// دالة لقياس الأداء
export function measurePerformance(name, fn) {
  return async (...args) => {
    const start = performance.now();
    const result = await fn(...args);
    const end = performance.now();
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`⚡ ${name}: ${(end - start).toFixed(2)}ms`);
    }
    
    return result;
  };
}

// دالة لتحسين الصور
export function getOptimizedImageProps(src, alt, size = 'medium') {
  return {
    src,
    alt,
    sizes: imageOptimization.sizes[size],
    quality: imageOptimization.quality,
    placeholder: imageOptimization.placeholder,
    blurDataURL: imageOptimization.blurDataURL,
  };
}

// دالة لتحسين تحميل المكونات
export function lazyLoad(importFunc, options = {}) {
  if (typeof window === 'undefined') {
    // في الخادم، إرجاع مكون فارغ
    return () => null;
  }

  const dynamic = require('next/dynamic');

  const LazyComponent = dynamic(importFunc, {
    loading: () => (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    ),
    ssr: options.ssr !== false,
    ...options,
  });

  return LazyComponent;
}

// تصدير جميع التحسينات
export default {
  imageOptimization,
  fontOptimization,
  scriptOptimization,
  cssOptimization,
  apiOptimization,
  performanceMonitoring,
  seoOptimization,
  pwaOptimization,
  measurePerformance,
  getOptimizedImageProps,
  lazyLoad,
};
