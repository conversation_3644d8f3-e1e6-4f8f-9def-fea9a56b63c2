import { useState, useEffect } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import ImageUpload from '../../components/admin/ImageUpload';
import { FaSave, FaEye, FaPlus, FaTrash, FaEdit } from 'react-icons/fa';
import Link from 'next/link';

export default function AboutUsManagement() {
  const [formData, setFormData] = useState({
    title: '',
    subtitle: '',
    description: '',
    vision: {
      title: 'رؤيتنا',
      content: '',
      icon: 'FaEye'
    },
    mission: {
      title: 'رسالتنا',
      content: '',
      icon: 'FaFlag'
    },
    values: {
      title: 'قيمنا',
      content: '',
      icon: 'FaHeart'
    },
    history: {
      title: 'تاريخنا',
      content: '',
      foundedYear: new Date().getFullYear(),
      milestones: []
    },
    statistics: [],
    images: {
      hero: '',
      about: ''
    },
    contact: {
      address: '',
      phone: '',
      email: '',
      workingHours: '',
      socialMedia: {
        facebook: '',
        twitter: '',
        linkedin: '',
        instagram: ''
      }
    },
    seo: {
      metaTitle: '',
      metaDescription: '',
      keywords: ''
    },
    status: 'published'
  });

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('basic');

  // جلب البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchAboutUs();
  }, []);

  const fetchAboutUs = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/about-us');
      const data = await response.json();

      if (data.success) {
        setFormData(data.aboutUs);
      } else {
        setError(data.message);
      }
    } catch (error) {
      console.error('Error fetching about us:', error);
      setError('حدث خطأ في جلب البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name.includes('.')) {
      const [parent, child, grandchild] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: grandchild ? {
            ...prev[parent][child],
            [grandchild]: value
          } : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }

    setError('');
    setSuccess('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch('/api/admin/about-us', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('تم حفظ البيانات بنجاح');
        setFormData(data.aboutUs);
      } else {
        setError(data.message);
      }
    } catch (error) {
      console.error('Error saving about us:', error);
      setError('حدث خطأ في حفظ البيانات');
    } finally {
      setSaving(false);
    }
  };

  // إضافة إحصائية جديدة
  const addStatistic = () => {
    setFormData(prev => ({
      ...prev,
      statistics: [
        ...prev.statistics,
        {
          title: '',
          value: '',
          suffix: '',
          icon: 'FaChartBar',
          order: prev.statistics.length + 1
        }
      ]
    }));
  };

  // حذف إحصائية
  const removeStatistic = (index) => {
    setFormData(prev => ({
      ...prev,
      statistics: prev.statistics.filter((_, i) => i !== index)
    }));
  };

  // تحديث إحصائية
  const updateStatistic = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      statistics: prev.statistics.map((stat, i) =>
        i === index ? { ...stat, [field]: value } : stat
      )
    }));
  };

  if (loading) {
    return (
      <AdminLayout title="إدارة صفحة من نحن">
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="إدارة صفحة من نحن">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">إدارة صفحة من نحن</h1>
          <div className="flex items-center space-x-4 space-x-reverse">
            <Link href="/about" target="_blank">
              <button className="btn-secondary flex items-center">
                <FaEye className="ml-2" />
                معاينة الصفحة
              </button>
            </Link>
          </div>
        </div>

        {/* Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
            {success}
          </div>
        )}

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 space-x-reverse">
            {[
              { id: 'basic', label: 'المعلومات الأساسية' },
              { id: 'vision', label: 'الرؤية والرسالة' },
              { id: 'statistics', label: 'الإحصائيات' },
              { id: 'images', label: 'الصور' },
              { id: 'contact', label: 'معلومات الاتصال' },
              { id: 'seo', label: 'SEO' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-md p-6">
          {/* المعلومات الأساسية */}
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">المعلومات الأساسية</h3>

              <div>
                <label className="block text-gray-700 font-semibold mb-2">
                  عنوان الصفحة *
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="من نحن"
                />
              </div>

              <div>
                <label className="block text-gray-700 font-semibold mb-2">
                  العنوان الفرعي
                </label>
                <input
                  type="text"
                  name="subtitle"
                  value={formData.subtitle}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="مكتب محاماة متخصص في تقديم الخدمات القانونية المتميزة"
                />
              </div>

              <div>
                <label className="block text-gray-700 font-semibold mb-2">
                  الوصف *
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  required
                  rows="6"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="وصف شامل عن المكتب وخدماته..."
                />
              </div>
            </div>
          )}

          {/* الرؤية والرسالة والقيم */}
          {activeTab === 'vision' && (
            <div className="space-y-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">الرؤية والرسالة والقيم</h3>

              {/* الرؤية */}
              <div className="border border-gray-200 rounded-lg p-6">
                <h4 className="text-md font-semibold text-gray-800 mb-4">الرؤية</h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">العنوان</label>
                    <input
                      type="text"
                      name="vision.title"
                      value={formData.vision.title}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">الأيقونة</label>
                    <input
                      type="text"
                      name="vision.icon"
                      value={formData.vision.icon}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="FaEye"
                    />
                  </div>
                </div>
                <div className="mt-4">
                  <label className="block text-gray-700 font-medium mb-2">المحتوى</label>
                  <textarea
                    name="vision.content"
                    value={formData.vision.content}
                    onChange={handleInputChange}
                    rows="4"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>

              {/* الرسالة */}
              <div className="border border-gray-200 rounded-lg p-6">
                <h4 className="text-md font-semibold text-gray-800 mb-4">الرسالة</h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">العنوان</label>
                    <input
                      type="text"
                      name="mission.title"
                      value={formData.mission.title}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">الأيقونة</label>
                    <input
                      type="text"
                      name="mission.icon"
                      value={formData.mission.icon}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="FaFlag"
                    />
                  </div>
                </div>
                <div className="mt-4">
                  <label className="block text-gray-700 font-medium mb-2">المحتوى</label>
                  <textarea
                    name="mission.content"
                    value={formData.mission.content}
                    onChange={handleInputChange}
                    rows="4"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>

              {/* القيم */}
              <div className="border border-gray-200 rounded-lg p-6">
                <h4 className="text-md font-semibold text-gray-800 mb-4">القيم</h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">العنوان</label>
                    <input
                      type="text"
                      name="values.title"
                      value={formData.values.title}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">الأيقونة</label>
                    <input
                      type="text"
                      name="values.icon"
                      value={formData.values.icon}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="FaHeart"
                    />
                  </div>
                </div>
                <div className="mt-4">
                  <label className="block text-gray-700 font-medium mb-2">المحتوى</label>
                  <textarea
                    name="values.content"
                    value={formData.values.content}
                    onChange={handleInputChange}
                    rows="4"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>
            </div>
          )}

          {/* الإحصائيات */}
          {activeTab === 'statistics' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">الإحصائيات</h3>
                <button
                  type="button"
                  onClick={addStatistic}
                  className="btn-primary flex items-center"
                >
                  <FaPlus className="ml-2" />
                  إضافة إحصائية
                </button>
              </div>

              {formData.statistics.map((stat, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-md font-semibold text-gray-800">إحصائية {index + 1}</h4>
                    <button
                      type="button"
                      onClick={() => removeStatistic(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <FaTrash />
                    </button>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">العنوان</label>
                      <input
                        type="text"
                        value={stat.title}
                        onChange={(e) => updateStatistic(index, 'title', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="سنوات الخبرة"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">القيمة</label>
                      <input
                        type="text"
                        value={stat.value}
                        onChange={(e) => updateStatistic(index, 'value', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="15"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">اللاحقة</label>
                      <input
                        type="text"
                        value={stat.suffix}
                        onChange={(e) => updateStatistic(index, 'suffix', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="+"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">الأيقونة</label>
                      <input
                        type="text"
                        value={stat.icon}
                        onChange={(e) => updateStatistic(index, 'icon', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="FaCalendar"
                      />
                    </div>
                  </div>
                </div>
              ))}

              {formData.statistics.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  لا توجد إحصائيات. اضغط على "إضافة إحصائية" لإضافة إحصائية جديدة.
                </div>
              )}
            </div>
          )}

          {/* الصور */}
          {activeTab === 'images' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">الصور</h3>

              <ImageUpload
                value={formData.images.hero}
                onChange={(url) => setFormData(prev => ({
                  ...prev,
                  images: { ...prev.images, hero: url }
                }))}
                label="صورة البانر الرئيسي"
                placeholder="اضغط لرفع صورة البانر أو اسحب الصورة هنا"
              />

              <ImageUpload
                value={formData.images.about}
                onChange={(url) => setFormData(prev => ({
                  ...prev,
                  images: { ...prev.images, about: url }
                }))}
                label="صورة قسم من نحن"
                placeholder="اضغط لرفع صورة قسم من نحن أو اسحب الصورة هنا"
              />
            </div>
          )}

          {/* معلومات الاتصال */}
          {activeTab === 'contact' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات الاتصال</h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <label className="block text-gray-700 font-medium mb-2">العنوان</label>
                  <input
                    type="text"
                    name="contact.address"
                    value={formData.contact.address}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-gray-700 font-medium mb-2">رقم الهاتف</label>
                  <input
                    type="text"
                    name="contact.phone"
                    value={formData.contact.phone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-gray-700 font-medium mb-2">البريد الإلكتروني</label>
                  <input
                    type="email"
                    name="contact.email"
                    value={formData.contact.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-gray-700 font-medium mb-2">ساعات العمل</label>
                  <input
                    type="text"
                    name="contact.workingHours"
                    value={formData.contact.workingHours}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>
            </div>
          )}

          {/* SEO */}
          {activeTab === 'seo' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">إعدادات SEO</h3>

              <div>
                <label className="block text-gray-700 font-medium mb-2">عنوان SEO</label>
                <input
                  type="text"
                  name="seo.metaTitle"
                  value={formData.seo.metaTitle}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  maxLength="60"
                />
                <p className="text-sm text-gray-500 mt-1">
                  {formData.seo.metaTitle.length}/60 حرف
                </p>
              </div>

              <div>
                <label className="block text-gray-700 font-medium mb-2">وصف SEO</label>
                <textarea
                  name="seo.metaDescription"
                  value={formData.seo.metaDescription}
                  onChange={handleInputChange}
                  rows="3"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  maxLength="160"
                />
                <p className="text-sm text-gray-500 mt-1">
                  {formData.seo.metaDescription.length}/160 حرف
                </p>
              </div>

              <div>
                <label className="block text-gray-700 font-medium mb-2">الكلمات المفتاحية</label>
                <input
                  type="text"
                  name="seo.keywords"
                  value={formData.seo.keywords}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="مكتب محاماة، خدمات قانونية، استشارات قانونية"
                />
              </div>
            </div>
          )}

          {/* أزرار الحفظ */}
          <div className="flex items-center justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200 mt-8">
            <button
              type="submit"
              disabled={saving}
              className="btn-primary flex items-center"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <FaSave className="ml-2" />
                  حفظ التغييرات
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
}
