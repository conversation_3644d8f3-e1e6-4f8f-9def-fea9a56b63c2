import mongoose from 'mongoose';

const teamMemberSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'اسم عضو الفريق مطلوب'],
    trim: true,
    maxlength: [100, 'الاسم يجب أن يكون أقل من 100 حرف']
  },
  position: {
    type: String,
    required: [true, 'المنصب مطلوب'],
    trim: true,
    maxlength: [100, 'المنصب يجب أن يكون أقل من 100 حرف']
  },
  bio: {
    type: String,
    trim: true,
    maxlength: [1000, 'السيرة الذاتية يجب أن تكون أقل من 1000 حرف']
  },
  image: {
    type: String,
    trim: true
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'البريد الإلكتروني غير صحيح']
  },
  phone: {
    type: String,
    trim: true,
    match: [/^[0-9+\-\s()]+$/, 'رقم الهاتف غير صحيح']
  },
  specializations: [{
    type: String,
    trim: true
  }],
  education: [{
    degree: {
      type: String,
      required: true,
      trim: true
    },
    institution: {
      type: String,
      required: true,
      trim: true
    },
    year: {
      type: Number,
      min: 1950,
      max: new Date().getFullYear()
    },
    description: {
      type: String,
      trim: true
    }
  }],
  experience: [{
    position: {
      type: String,
      required: true,
      trim: true
    },
    company: {
      type: String,
      required: true,
      trim: true
    },
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date
    },
    current: {
      type: Boolean,
      default: false
    },
    description: {
      type: String,
      trim: true
    }
  }],
  achievements: [{
    title: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    date: {
      type: Date
    },
    category: {
      type: String,
      enum: ['award', 'certification', 'publication', 'speaking', 'other'],
      default: 'other'
    }
  }],
  languages: [{
    language: {
      type: String,
      required: true,
      trim: true
    },
    level: {
      type: String,
      enum: ['basic', 'intermediate', 'advanced', 'native'],
      required: true
    }
  }],
  socialMedia: {
    linkedin: {
      type: String,
      trim: true
    },
    twitter: {
      type: String,
      trim: true
    },
    facebook: {
      type: String,
      trim: true
    },
    instagram: {
      type: String,
      trim: true
    },
    website: {
      type: String,
      trim: true
    }
  },
  order: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'former'],
    default: 'active'
  },
  isPartner: {
    type: Boolean,
    default: false
  },
  joinDate: {
    type: Date,
    default: Date.now
  },
  leaveDate: {
    type: Date
  },
  officeLocation: {
    type: String,
    trim: true
  },
  workingHours: {
    type: String,
    trim: true
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
});

// فهرسة للبحث والأداء
teamMemberSchema.index({ status: 1 });
teamMemberSchema.index({ order: 1 });
teamMemberSchema.index({ isPartner: 1 });
teamMemberSchema.index({ specializations: 1 });
teamMemberSchema.index({ 
  name: 'text', 
  position: 'text', 
  bio: 'text',
  specializations: 'text'
}, {
  weights: {
    name: 10,
    position: 5,
    specializations: 3,
    bio: 1
  }
});

// Virtual لحساب سنوات الخبرة
teamMemberSchema.virtual('yearsOfExperience').get(function() {
  if (!this.joinDate) return 0;
  const endDate = this.leaveDate || new Date();
  const years = (endDate - this.joinDate) / (1000 * 60 * 60 * 24 * 365.25);
  return Math.floor(years);
});

// Virtual للحصول على التعليم الأحدث
teamMemberSchema.virtual('latestEducation').get(function() {
  if (!this.education || this.education.length === 0) return null;
  return this.education.sort((a, b) => (b.year || 0) - (a.year || 0))[0];
});

// Virtual للحصول على المنصب الحالي
teamMemberSchema.virtual('currentPosition').get(function() {
  const currentExp = this.experience.find(exp => exp.current);
  return currentExp ? currentExp.position : this.position;
});

// طرق النموذج
teamMemberSchema.methods.addSpecialization = function(specialization) {
  if (!this.specializations.includes(specialization)) {
    this.specializations.push(specialization);
    return this.save();
  }
  return this;
};

teamMemberSchema.methods.removeSpecialization = function(specialization) {
  this.specializations = this.specializations.filter(s => s !== specialization);
  return this.save();
};

teamMemberSchema.methods.addEducation = function(education) {
  this.education.push(education);
  return this.save();
};

teamMemberSchema.methods.addExperience = function(experience) {
  // إذا كان المنصب الجديد حالي، قم بإلغاء الحالي من المناصب الأخرى
  if (experience.current) {
    this.experience.forEach(exp => {
      exp.current = false;
    });
  }
  
  this.experience.push(experience);
  return this.save();
};

teamMemberSchema.methods.addAchievement = function(achievement) {
  this.achievements.push(achievement);
  return this.save();
};

teamMemberSchema.methods.activate = function() {
  this.status = 'active';
  this.leaveDate = null;
  return this.save();
};

teamMemberSchema.methods.deactivate = function() {
  this.status = 'inactive';
  return this.save();
};

teamMemberSchema.methods.markAsFormer = function(leaveDate = new Date()) {
  this.status = 'former';
  this.leaveDate = leaveDate;
  return this.save();
};

// طرق ثابتة
teamMemberSchema.statics.findActive = function() {
  return this.find({ status: 'active' }).sort({ order: 1, name: 1 });
};

teamMemberSchema.statics.findPartners = function() {
  return this.find({ status: 'active', isPartner: true }).sort({ order: 1, name: 1 });
};

teamMemberSchema.statics.findBySpecialization = function(specialization) {
  return this.find({ 
    status: 'active',
    specializations: { $in: [specialization] }
  }).sort({ order: 1, name: 1 });
};

teamMemberSchema.statics.searchMembers = function(query) {
  return this.find(
    { 
      $text: { $search: query },
      status: 'active'
    },
    { score: { $meta: 'textScore' } }
  ).sort({ score: { $meta: 'textScore' } });
};

teamMemberSchema.statics.reorderMembers = async function(memberIds) {
  const updates = memberIds.map((id, index) => ({
    updateOne: {
      filter: { _id: id },
      update: { order: index }
    }
  }));
  
  return this.bulkWrite(updates);
};

teamMemberSchema.statics.getStats = async function() {
  const pipeline = [
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ];
  
  const statusStats = await this.aggregate(pipeline);
  const total = await this.countDocuments();
  const partners = await this.countDocuments({ isPartner: true, status: 'active' });
  
  const stats = {
    total,
    active: 0,
    inactive: 0,
    former: 0,
    partners
  };
  
  statusStats.forEach(stat => {
    stats[stat._id] = stat.count;
  });
  
  return stats;
};

// تصدير النموذج
const TeamMember = mongoose.models.TeamMember || mongoose.model('TeamMember', teamMemberSchema);

export default TeamMember;
