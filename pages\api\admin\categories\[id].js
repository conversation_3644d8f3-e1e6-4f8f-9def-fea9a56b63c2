export default async function handler(req, res) {
  const { id } = req.query;

  switch (req.method) {
    case 'GET':
      return getCategory(req, res, id);
    case 'PUT':
      return updateCategory(req, res, id);
    case 'DELETE':
      return deleteCategory(req, res, id);
    default:
      return res.status(405).json({
        success: false,
        message: 'Method not allowed'
      });
  }
}

// جلب تصنيف واحد
const getCategory = async (req, res, id) => {
  try {
    const fs = require('fs');
    const path = require('path');
    const categoriesPath = path.join(process.cwd(), 'data', 'categories.json');

    let categories = [];
    if (fs.existsSync(categoriesPath)) {
      const data = fs.readFileSync(categoriesPath, 'utf8');
      categories = JSON.parse(data);
    }

    const category = categories.find(cat => cat.id === parseInt(id));

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'التصنيف غير موجود'
      });
    }

    return res.status(200).json({
      success: true,
      category
    });
  } catch (error) {
    console.error('Error fetching category:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب التصنيف'
    });
  }
};

// تحديث تصنيف
const updateCategory = async (req, res, id) => {
  const { name, slug, description, color } = req.body;

  if (!name) {
    return res.status(400).json({
      success: false,
      message: 'اسم التصنيف مطلوب'
    });
  }

  try {
    const fs = require('fs');
    const path = require('path');
    const categoriesPath = path.join(process.cwd(), 'data', 'categories.json');

    let categories = [];
    if (fs.existsSync(categoriesPath)) {
      const data = fs.readFileSync(categoriesPath, 'utf8');
      categories = JSON.parse(data);
    }

    const categoryIndex = categories.findIndex(cat => cat.id === parseInt(id));

    if (categoryIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'التصنيف غير موجود'
      });
    }

    // التحقق من عدم تكرار الاسم أو الـ slug (باستثناء التصنيف الحالي)
    const existingCategory = categories.find(cat => 
      cat.id !== parseInt(id) && (cat.name === name || cat.slug === slug)
    );
    
    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: 'اسم التصنيف أو الرابط المختصر موجود مسبقاً'
      });
    }

    // تحديث التصنيف
    const updatedCategory = {
      ...categories[categoryIndex],
      name,
      slug: slug || categories[categoryIndex].slug,
      description: description || '',
      color: color || categories[categoryIndex].color,
      updated_at: new Date().toISOString()
    };

    categories[categoryIndex] = updatedCategory;

    // حفظ البيانات
    fs.writeFileSync(categoriesPath, JSON.stringify(categories, null, 2));

    return res.status(200).json({
      success: true,
      message: 'تم تحديث التصنيف بنجاح',
      category: updatedCategory
    });
  } catch (error) {
    console.error('Error updating category:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في تحديث التصنيف'
    });
  }
};

// حذف تصنيف
const deleteCategory = async (req, res, id) => {
  try {
    const fs = require('fs');
    const path = require('path');
    const categoriesPath = path.join(process.cwd(), 'data', 'categories.json');
    const articlesPath = path.join(process.cwd(), 'data', 'articles.json');

    let categories = [];
    if (fs.existsSync(categoriesPath)) {
      const data = fs.readFileSync(categoriesPath, 'utf8');
      categories = JSON.parse(data);
    }

    const categoryIndex = categories.findIndex(cat => cat.id === parseInt(id));

    if (categoryIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'التصنيف غير موجود'
      });
    }

    const categoryToDelete = categories[categoryIndex];

    // التحقق من وجود مقالات تستخدم هذا التصنيف
    let articles = [];
    if (fs.existsSync(articlesPath)) {
      const articlesData = fs.readFileSync(articlesPath, 'utf8');
      articles = JSON.parse(articlesData);
    }

    const articlesUsingCategory = articles.filter(article => 
      article.category === categoryToDelete.name
    );

    if (articlesUsingCategory.length > 0) {
      return res.status(400).json({
        success: false,
        message: `لا يمكن حذف التصنيف لأنه مستخدم في ${articlesUsingCategory.length} مقال(ات)`
      });
    }

    // حذف التصنيف
    categories.splice(categoryIndex, 1);

    // حفظ البيانات
    fs.writeFileSync(categoriesPath, JSON.stringify(categories, null, 2));

    return res.status(200).json({
      success: true,
      message: 'تم حذف التصنيف بنجاح',
      category: categoryToDelete
    });
  } catch (error) {
    console.error('Error deleting category:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في حذف التصنيف'
    });
  }
};
