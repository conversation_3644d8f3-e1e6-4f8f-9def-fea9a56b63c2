import { useState, useRef } from 'react';
import { FaImage, FaUpload, FaTrash, FaEye, FaTimes, FaCopy } from 'react-icons/fa';

const ImageManager = ({ onImageSelect, existingImages = [] }) => {
  const [uploading, setUploading] = useState(false);
  const [uploadedImages, setUploadedImages] = useState(existingImages);
  const [previewImage, setPreviewImage] = useState(null);
  const fileInputRef = useRef(null);

  const handleFileSelect = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    setUploading(true);

    try {
      for (const file of files) {
        // التحقق من نوع الملف
        if (!file.type.startsWith('image/')) {
          alert(`${file.name}: يُسمح فقط بملفات الصور`);
          continue;
        }

        // التحقق من حجم الملف (5MB)
        if (file.size > 5 * 1024 * 1024) {
          alert(`${file.name}: حجم الملف يجب أن يكون أقل من 5 ميجابايت`);
          continue;
        }

        const formData = new FormData();
        formData.append('image', file);

        const response = await fetch('/api/upload/image', {
          method: 'POST',
          body: formData,
        });

        const result = await response.json();

        if (result.success) {
          setUploadedImages(prev => [...prev, result.image]);
        } else {
          alert(`${file.name}: ${result.error || 'خطأ في رفع الصورة'}`);
        }
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('خطأ في رفع الصور');
    } finally {
      setUploading(false);
      // إعادة تعيين input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveImage = (index) => {
    if (confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
      setUploadedImages(prev => prev.filter((_, i) => i !== index));
    }
  };

  const handlePreview = (image) => {
    setPreviewImage(image);
  };

  const handleCopyUrl = (url) => {
    navigator.clipboard.writeText(url);
    alert('تم نسخ رابط الصورة');
  };

  const handleSelectImage = (image) => {
    if (onImageSelect) {
      onImageSelect(image);
    }
  };

  return (
    <div className="image-manager">
      {/* منطقة رفع الصور */}
      <div className="upload-area">
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple
          onChange={handleFileSelect}
          className="hidden"
          id="image-upload"
        />
        <label
          htmlFor="image-upload"
          className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
            uploading
              ? 'border-gray-300 bg-gray-50 cursor-not-allowed'
              : 'border-blue-300 bg-blue-50 hover:bg-blue-100'
          }`}
        >
          {uploading ? (
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
              <p className="text-sm text-gray-600">جاري رفع الصور...</p>
            </div>
          ) : (
            <div className="flex flex-col items-center">
              <FaUpload className="w-8 h-8 text-blue-600 mb-2" />
              <p className="text-sm text-gray-600 text-center">
                اضغط لاختيار الصور أو اسحبها هنا
                <br />
                <span className="text-xs text-gray-500">يمكن اختيار عدة صور معاً (الحد الأقصى: 5 ميجابايت لكل صورة)</span>
              </p>
            </div>
          )}
        </label>
      </div>

      {/* معرض الصور */}
      {uploadedImages.length > 0 && (
        <div className="images-gallery mt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">الصور المرفوعة:</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {uploadedImages.map((image, index) => (
              <div
                key={index}
                className="relative group bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
              >
                <div className="aspect-square">
                  <img
                    src={image.url}
                    alt={image.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* طبقة التحكم */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <div className="flex space-x-2 space-x-reverse">
                    <button
                      onClick={() => handlePreview(image)}
                      className="p-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 transition-colors"
                      title="معاينة"
                    >
                      <FaEye />
                    </button>
                    <button
                      onClick={() => handleCopyUrl(image.url)}
                      className="p-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 transition-colors"
                      title="نسخ الرابط"
                    >
                      <FaCopy />
                    </button>
                    <button
                      onClick={() => handleSelectImage(image)}
                      className="p-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                      title="اختيار"
                    >
                      <FaImage />
                    </button>
                    <button
                      onClick={() => handleRemoveImage(index)}
                      className="p-2 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors"
                      title="حذف"
                    >
                      <FaTrash />
                    </button>
                  </div>
                </div>

                {/* معلومات الصورة */}
                <div className="p-2">
                  <p className="text-xs text-gray-600 truncate" title={image.name}>
                    {image.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {(image.size / 1024 / 1024).toFixed(2)} ميجابايت
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* نافذة المعاينة */}
      {previewImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-4 max-w-4xl max-h-[90vh] w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">{previewImage.name}</h3>
              <button
                onClick={() => setPreviewImage(null)}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <FaTimes />
              </button>
            </div>
            <div className="max-h-96 overflow-auto">
              <img
                src={previewImage.url}
                alt={previewImage.name}
                className="w-full h-auto rounded"
              />
            </div>
            <div className="flex justify-end space-x-2 space-x-reverse mt-4">
              <button
                onClick={() => handleCopyUrl(previewImage.url)}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors flex items-center space-x-2 space-x-reverse"
              >
                <FaCopy />
                <span>نسخ الرابط</span>
              </button>
              <button
                onClick={() => handleSelectImage(previewImage)}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors flex items-center space-x-2 space-x-reverse"
              >
                <FaImage />
                <span>اختيار الصورة</span>
              </button>
              <button
                onClick={() => setPreviewImage(null)}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageManager;
