import { createContext, useContext, useState, useEffect } from 'react';

// إنشاء قيم افتراضية للسياق
const defaultAuthValue = {
  user: null,
  loading: true,
  isAuthenticated: false,
  login: async () => ({ success: false, message: 'Auth not initialized' }),
  logout: () => {}
};

const AuthContext = createContext(defaultAuthValue);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    console.warn('useAuth called outside AuthProvider, returning default values');
    return defaultAuthValue;
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // التحقق من المصادقة مرة واحدة فقط عند تحميل التطبيق
  useEffect(() => {
    const initAuth = () => {
      try {
        // التحقق من وجود localStorage (متاح فقط في العميل)
        if (typeof window !== 'undefined' && window.localStorage) {
          const token = localStorage.getItem('adminToken');
          const userData = localStorage.getItem('adminUser');

          if (token && userData) {
            try {
              const parsedUser = JSON.parse(userData);
              setUser(parsedUser);
              setIsAuthenticated(true);
            } catch (e) {
              console.error('Error parsing user data:', e);
              localStorage.removeItem('adminToken');
              localStorage.removeItem('adminUser');
            }
          }
        }
      } catch (error) {
        console.error('Auth initialization failed:', error);
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (email, password) => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (data.success) {
        // حفظ البيانات (فقط في العميل)
        if (typeof window !== 'undefined' && window.localStorage) {
          localStorage.setItem('adminToken', data.token);
          localStorage.setItem('adminUser', JSON.stringify(data.user));
        }

        setUser(data.user);
        setIsAuthenticated(true);

        return { success: true, user: data.user };
      } else {
        return { success: false, message: data.message };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, message: 'خطأ في الاتصال' };
    }
  };

  const logout = () => {
    // إزالة البيانات من localStorage (فقط في العميل)
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminUser');
    }

    // تحديث الحالة
    setUser(null);
    setIsAuthenticated(false);

    // إعادة تحميل الصفحة للتأكد من مسح جميع البيانات (فقط في العميل)
    if (typeof window !== 'undefined') {
      window.location.href = '/admin/login';
    }
  };

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
