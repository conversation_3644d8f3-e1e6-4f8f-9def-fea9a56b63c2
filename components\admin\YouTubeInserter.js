import { useState } from 'react';
import { FaYoutube, FaPlay, FaTimes, FaCheck, FaExclamationTriangle } from 'react-icons/fa';

const YouTubeInserter = ({ onInsert, onClose }) => {
  const [url, setUrl] = useState('');
  const [videoId, setVideoId] = useState('');
  const [videoInfo, setVideoInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const extractYouTubeVideoId = (url) => {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /youtube\.com\/watch\?.*v=([^&\n?#]+)/,
      /youtu\.be\/([^&\n?#]+)/,
      /youtube\.com\/embed\/([^&\n?#]+)/,
      /youtube\.com\/v\/([^&\n?#]+)/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }
    return null;
  };

  const handleUrlChange = (e) => {
    const newUrl = e.target.value;
    setUrl(newUrl);
    setError('');
    
    if (newUrl.trim()) {
      const id = extractYouTubeVideoId(newUrl);
      if (id) {
        setVideoId(id);
        setVideoInfo({
          id: id,
          thumbnail: `https://img.youtube.com/vi/${id}/maxresdefault.jpg`,
          embedUrl: `https://www.youtube.com/embed/${id}`
        });
      } else {
        setVideoId('');
        setVideoInfo(null);
        if (newUrl.length > 10) {
          setError('رابط YouTube غير صحيح');
        }
      }
    } else {
      setVideoId('');
      setVideoInfo(null);
    }
  };

  const createYouTubeEmbed = (videoId) => {
    return `<p style="text-align: center; margin: 20px 0; padding: 20px; background: #ff0000; color: white; border-radius: 10px; font-family: Arial, sans-serif;">
📺 <strong>فيديو YouTube</strong><br>
<span style="font-size: 14px;">معرف الفيديو: ${videoId}</span><br>
<span style="font-size: 12px; opacity: 0.8;">سيتم عرض الفيديو في المقال المنشور</span>
</p>`;
  };

  const handleInsert = () => {
    if (videoId) {
      const embedHTML = createYouTubeEmbed(videoId);
      console.log('Inserting YouTube video:', videoId);
      console.log('Embed HTML:', embedHTML);
      onInsert(embedHTML);
      onClose();
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && videoId) {
      handleInsert();
    }
  };

  return (
    <div className="youtube-inserter">
      <div className="youtube-inserter-header">
        <div className="flex items-center gap-2">
          <FaYoutube className="text-red-600 text-xl" />
          <h3 className="text-lg font-semibold text-gray-900">إدراج فيديو YouTube</h3>
        </div>
        <button
          onClick={onClose}
          className="p-2 hover:bg-gray-100 rounded-full transition-colors"
        >
          <FaTimes className="text-gray-500" />
        </button>
      </div>

      <div className="youtube-inserter-content">
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            رابط فيديو YouTube
          </label>
          <input
            type="url"
            value={url}
            onChange={handleUrlChange}
            onKeyPress={handleKeyPress}
            placeholder="https://www.youtube.com/watch?v=..."
            className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 transition-colors ${
              error 
                ? 'border-red-500 focus:ring-red-500' 
                : videoId 
                ? 'border-green-500 focus:ring-green-500' 
                : 'border-gray-300 focus:ring-blue-500'
            }`}
          />
          
          {error && (
            <div className="mt-2 flex items-center gap-2 text-red-600 text-sm">
              <FaExclamationTriangle />
              <span>{error}</span>
            </div>
          )}

          {videoId && (
            <div className="mt-2 flex items-center gap-2 text-green-600 text-sm">
              <FaCheck />
              <span>تم التعرف على الفيديو بنجاح</span>
            </div>
          )}
        </div>

        {/* معاينة الفيديو */}
        {videoInfo && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              معاينة الفيديو
            </label>
            <div className="relative bg-gray-100 rounded-lg overflow-hidden">
              <div className="aspect-video relative">
                <img
                  src={videoInfo.thumbnail}
                  alt="معاينة الفيديو"
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.src = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
                  }}
                />
                <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                  <div className="bg-red-600 text-white p-3 rounded-full">
                    <FaPlay className="text-xl" />
                  </div>
                </div>
                <div className="absolute top-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs flex items-center gap-1">
                  <FaYoutube className="text-red-500" />
                  YouTube
                </div>
              </div>
            </div>
          </div>
        )}

        {/* أمثلة على الروابط المدعومة */}
        <div className="mb-4 p-3 bg-blue-50 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">أمثلة على الروابط المدعومة:</h4>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• https://www.youtube.com/watch?v=VIDEO_ID</li>
            <li>• https://youtu.be/VIDEO_ID</li>
            <li>• https://www.youtube.com/embed/VIDEO_ID</li>
          </ul>
        </div>
      </div>

      <div className="youtube-inserter-footer">
        <button
          onClick={onClose}
          className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          إلغاء
        </button>
        <button
          onClick={handleInsert}
          disabled={!videoId}
          className={`px-6 py-2 rounded-lg font-medium transition-colors flex items-center gap-2 ${
            videoId
              ? 'bg-red-600 text-white hover:bg-red-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          <FaYoutube />
          إدراج الفيديو
        </button>
      </div>

      <style jsx>{`
        .youtube-inserter {
          background: white;
          border-radius: 12px;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
          width: 100%;
          max-width: 500px;
          overflow: hidden;
        }

        .youtube-inserter-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px;
          border-bottom: 1px solid #e5e7eb;
          background: #f9fafb;
        }

        .youtube-inserter-content {
          padding: 20px;
        }

        .youtube-inserter-footer {
          display: flex;
          justify-content: flex-end;
          gap: 12px;
          padding: 20px;
          border-top: 1px solid #e5e7eb;
          background: #f9fafb;
        }

        @media (max-width: 640px) {
          .youtube-inserter {
            max-width: 100%;
            margin: 10px;
          }
          
          .youtube-inserter-header,
          .youtube-inserter-content,
          .youtube-inserter-footer {
            padding: 16px;
          }
        }
      `}</style>
    </div>
  );
};

export default YouTubeInserter;
