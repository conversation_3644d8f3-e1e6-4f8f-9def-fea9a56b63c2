import Link from 'next/link';
import { FaPhone, FaEnvelope, FaMapMarkerAlt, FaFacebook, FaTwitter, FaLinkedin, FaInstagram } from 'react-icons/fa';

function Footer() {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { name: 'الرئيسية', href: '/' },
    { name: 'من نحن', href: '/about' },
    { name: 'خدماتنا', href: '/services' },
    { name: 'فريق العمل', href: '/team' },
    { name: 'اتصل بنا', href: '/contact' },
  ];

  const services = [
    'قانون الشركات والتجارة',
    'التقاضي وحل النزاعات',
    'القانون العقاري',
    'قانون العمل والتوظيف',
    'الملكية الفكرية',
    'الاستشارات الضريبية',
  ];

  const socialLinks = [
    { icon: FaFacebook, href: '#', label: 'Facebook' },
    { icon: FaTwitter, href: '#', label: 'Twitter' },
    { icon: FaLinkedin, href: '#', label: 'LinkedIn' },
    { icon: FaInstagram, href: '#', label: 'Instagram' },
  ];

  return (
    <footer className="bg-primary-950 text-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="text-2xl font-bold">
              مكتب المحاماة
              <span className="text-gold-400 block text-sm font-normal">
                للاستشارات القانونية
              </span>
            </div>
            <p className="text-gray-300 leading-relaxed">
              نحن مكتب محاماة متخصص في تقديم الاستشارات القانونية والخدمات القانونية المتكاملة
              للأفراد والشركات بأعلى معايير الجودة والاحترافية.
            </p>
            <div className="flex space-x-4 space-x-reverse">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  className="text-gray-400 hover:text-gold-400 transition-colors duration-200"
                  aria-label={social.label}
                >
                  <social.icon size={20} />
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gold-400">روابط سريعة</h3>
            <ul className="space-y-2">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gold-400">خدماتنا</h3>
            <ul className="space-y-2">
              {services.map((service, index) => (
                <li key={index}>
                  <span className="text-gray-300 text-sm">
                    {service}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gold-400">معلومات التواصل</h3>
            <div className="space-y-3">
              <div className="flex items-start">
                <FaMapMarkerAlt className="text-[#B08D57] mt-1 ml-3 flex-shrink-0" />
                <span className="text-gray-300 text-sm">
                  القاهرة، جمهورية مصر العربية<br />
                  إحداثيات: 30°02'19.3"N 31°20'35.0"E
                </span>
              </div>
              <div className="flex items-center">
                <FaPhone className="text-[#B08D57] ml-3" />
                <span className="text-gray-300">+20 2 1234 5678</span>
              </div>
              <div className="flex items-center">
                <FaEnvelope className="text-[#B08D57] ml-3" />
                <span className="text-gray-300"><EMAIL></span>
              </div>
            </div>
            <div className="pt-4">
              <h4 className="text-sm font-semibold text-[#B08D57] mb-2">ساعات العمل</h4>
              <p className="text-gray-300 text-sm">
                السبت - الخميس: 9:00 ص - 6:00 م<br />
                الجمعة: مغلق
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © {currentYear} مكتب المحاماة للاستشارات القانونية. جميع الحقوق محفوظة.
            </p>
            <div className="flex space-x-6 space-x-reverse mt-4 md:mt-0">
              <Link href="/privacy" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                سياسة الخصوصية
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                شروط الاستخدام
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default Footer;
