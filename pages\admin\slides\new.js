import { useState } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '../../../components/admin/AdminLayout';
import ImageUpload from '../../../components/admin/ImageUpload';
import Link from 'next/link';
import { FaSave, FaArrowLeft, FaImage, FaUpload } from 'react-icons/fa';

export default function NewSlide() {
  const [formData, setFormData] = useState({
    title: '',
    subtitle: '',
    description: '',
    image: '',
    mobileImage: '',
    video: '',
    cta_text: '',
    cta_link: '',
    cta_style: 'primary',
    order_index: 0,
    status: 'active',
    startDate: '',
    endDate: '',
    animation: {
      type: 'fade',
      duration: 5000,
      direction: 'left'
    },
    overlay: {
      enabled: true,
      color: 'rgba(0, 0, 0, 0.4)',
      opacity: 0.4
    },
    textPosition: 'left',
    textColor: '#ffffff'
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const router = useRouter();

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    // التعامل مع الحقول المتداخلة
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : (type === 'number' ? Number(value) : value)
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : (type === 'number' ? Number(value) : value)
      }));
    }

    setError('');
    setSuccess('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch('/api/admin/slides', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('تم إنشاء الشريحة بنجاح');
        setTimeout(() => {
          router.push('/admin/slides');
        }, 1500);
      } else {
        setError(data.message);
      }
    } catch (error) {
      setError('حدث خطأ في إنشاء الشريحة');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminLayout title="إضافة شريحة جديدة">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إضافة شريحة جديدة</h1>
            <p className="text-gray-600">إنشاء شريحة جديدة للصفحة الرئيسية</p>
          </div>
          <Link href="/admin/slides">
            <button className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors duration-200 flex items-center">
              <FaArrowLeft className="ml-2" />
              العودة للقائمة
            </button>
          </Link>
        </div>

        {/* Form */}
        <div className="bg-white rounded-lg shadow p-6">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
              {error}
            </div>
          )}

          {success && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
              {success}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* العنوان الرئيسي */}
              <div>
                <label className="block text-gray-700 font-semibold mb-2">
                  العنوان الرئيسي *
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="أدخل العنوان الرئيسي"
                />
              </div>

              {/* العنوان الفرعي */}
              <div>
                <label className="block text-gray-700 font-semibold mb-2">
                  العنوان الفرعي
                </label>
                <input
                  type="text"
                  name="subtitle"
                  value={formData.subtitle}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="أدخل العنوان الفرعي"
                />
              </div>
            </div>

            {/* الوصف */}
            <div>
              <label className="block text-gray-700 font-semibold mb-2">
                الوصف
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows="4"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="أدخل وصف الشريحة"
              />
            </div>

            {/* صورة الشريحة */}
            <ImageUpload
              value={formData.image}
              onChange={(url) => setFormData(prev => ({ ...prev, image: url }))}
              label="صورة الشريحة"
              placeholder="اضغط لرفع صورة الشريحة أو اسحب الصورة هنا"
              required={true}
            />

            {/* صورة الجوال */}
            <ImageUpload
              value={formData.mobileImage}
              onChange={(url) => setFormData(prev => ({ ...prev, mobileImage: url }))}
              label="صورة الجوال (اختيارية)"
              placeholder="اضغط لرفع صورة مخصصة للجوال أو اسحب الصورة هنا"
              required={false}
            />

            {/* رابط الفيديو */}
            <div>
              <label className="block text-gray-700 font-semibold mb-2">
                رابط الفيديو (اختياري)
              </label>
              <input
                type="url"
                name="video"
                value={formData.video}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="رابط فيديو خلفية"
              />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* نص زر الإجراء */}
              <div>
                <label className="block text-gray-700 font-semibold mb-2">
                  نص زر الإجراء
                </label>
                <input
                  type="text"
                  name="cta_text"
                  value={formData.cta_text}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="مثال: اطلب استشارة الآن"
                />
              </div>

              {/* رابط زر الإجراء */}
              <div>
                <label className="block text-gray-700 font-semibold mb-2">
                  رابط زر الإجراء
                </label>
                <input
                  type="text"
                  name="cta_link"
                  value={formData.cta_link}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="/contact أو https://example.com"
                />
              </div>

              {/* نمط زر الإجراء */}
              <div>
                <label className="block text-gray-700 font-semibold mb-2">
                  نمط زر الإجراء
                </label>
                <select
                  name="cta_style"
                  value={formData.cta_style}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="primary">أساسي</option>
                  <option value="secondary">ثانوي</option>
                  <option value="outline">محدد</option>
                  <option value="ghost">شفاف</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* ترتيب العرض */}
              <div>
                <label className="block text-gray-700 font-semibold mb-2">
                  ترتيب العرض
                </label>
                <input
                  type="number"
                  name="order_index"
                  value={formData.order_index}
                  onChange={handleInputChange}
                  min="0"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="0"
                />
                <p className="text-sm text-gray-500 mt-1">
                  الرقم الأصغر يظهر أولاً
                </p>
              </div>

              {/* الحالة */}
              <div>
                <label className="block text-gray-700 font-semibold mb-2">
                  الحالة
                </label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="active">نشط</option>
                  <option value="inactive">غير نشط</option>
                  <option value="scheduled">مجدول</option>
                </select>
              </div>

              {/* موضع النص */}
              <div>
                <label className="block text-gray-700 font-semibold mb-2">
                  موضع النص
                </label>
                <select
                  name="textPosition"
                  value={formData.textPosition}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="left">يسار</option>
                  <option value="center">وسط</option>
                  <option value="right">يمين</option>
                </select>
              </div>
            </div>

            {/* تواريخ الجدولة */}
            {formData.status === 'scheduled' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <label className="block text-gray-700 font-semibold mb-2">
                    تاريخ البداية
                  </label>
                  <input
                    type="datetime-local"
                    name="startDate"
                    value={formData.startDate}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
                <div>
                  <label className="block text-gray-700 font-semibold mb-2">
                    تاريخ النهاية
                  </label>
                  <input
                    type="datetime-local"
                    name="endDate"
                    value={formData.endDate}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>
            )}

            {/* إعدادات متقدمة */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">الإعدادات المتقدمة</h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* لون النص */}
                <div>
                  <label className="block text-gray-700 font-semibold mb-2">
                    لون النص
                  </label>
                  <input
                    type="color"
                    name="textColor"
                    value={formData.textColor}
                    onChange={handleInputChange}
                    className="w-full h-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>

                {/* مدة الرسوم المتحركة */}
                <div>
                  <label className="block text-gray-700 font-semibold mb-2">
                    مدة العرض (بالثواني)
                  </label>
                  <input
                    type="number"
                    name="animation.duration"
                    value={formData.animation.duration / 1000}
                    onChange={(e) => {
                      const value = Number(e.target.value) * 1000;
                      setFormData(prev => ({
                        ...prev,
                        animation: { ...prev.animation, duration: value }
                      }));
                    }}
                    min="1"
                    max="30"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                {/* نوع الرسوم المتحركة */}
                <div>
                  <label className="block text-gray-700 font-semibold mb-2">
                    نوع الرسوم المتحركة
                  </label>
                  <select
                    name="animation.type"
                    value={formData.animation.type}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="fade">تلاشي</option>
                    <option value="slide">انزلاق</option>
                    <option value="zoom">تكبير</option>
                    <option value="none">بدون</option>
                  </select>
                </div>

                {/* اتجاه الرسوم المتحركة */}
                <div>
                  <label className="block text-gray-700 font-semibold mb-2">
                    اتجاه الرسوم المتحركة
                  </label>
                  <select
                    name="animation.direction"
                    value={formData.animation.direction}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="left">يسار</option>
                    <option value="right">يمين</option>
                    <option value="up">أعلى</option>
                    <option value="down">أسفل</option>
                  </select>
                </div>
              </div>

              {/* إعدادات التراكب */}
              <div className="mt-6">
                <div className="flex items-center mb-4">
                  <input
                    type="checkbox"
                    name="overlay.enabled"
                    checked={formData.overlay.enabled}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label className="mr-2 text-gray-700 font-semibold">
                    تفعيل طبقة التراكب
                  </label>
                </div>

                {formData.overlay.enabled && (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-gray-700 font-semibold mb-2">
                        لون التراكب
                      </label>
                      <input
                        type="text"
                        name="overlay.color"
                        value={formData.overlay.color}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="rgba(0, 0, 0, 0.4)"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 font-semibold mb-2">
                        شفافية التراكب
                      </label>
                      <input
                        type="range"
                        name="overlay.opacity"
                        value={formData.overlay.opacity}
                        onChange={handleInputChange}
                        min="0"
                        max="1"
                        step="0.1"
                        className="w-full"
                      />
                      <span className="text-sm text-gray-500">{formData.overlay.opacity}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* أزرار الإجراء */}
            <div className="flex justify-end space-x-4 space-x-reverse pt-6 border-t border-gray-200">
              <Link href="/admin/slides">
                <button
                  type="button"
                  className="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors duration-200"
                >
                  إلغاء
                </button>
              </Link>
              <button
                type="submit"
                disabled={loading}
                className={`px-6 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center ${
                  loading
                    ? 'bg-gray-400 cursor-not-allowed text-white'
                    : 'bg-primary-900 hover:bg-primary-800 text-white'
                }`}
              >
                {loading ? (
                  'جاري الحفظ...'
                ) : (
                  <>
                    <FaSave className="ml-2" />
                    حفظ الشريحة
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
