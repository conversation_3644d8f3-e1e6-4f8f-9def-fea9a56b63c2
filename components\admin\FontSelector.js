import { useState } from 'react';
import { FaFont, FaChe<PERSON> } from 'react-icons/fa';

const FontSelector = ({ selectedFont, onFontChange, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);

  const arabicFonts = [
    {
      name: 'Cairo',
      displayName: 'القاهرة',
      preview: 'نص تجريبي بخط القاهرة',
      category: 'sans-serif'
    },
    {
      name: '<PERSON>ja<PERSON>',
      displayName: 'تجوال',
      preview: 'نص تجريبي بخط تجوال',
      category: 'sans-serif'
    },
    {
      name: '<PERSON><PERSON>',
      displayName: 'أميري',
      preview: 'نص تجريبي بخط أميري',
      category: 'serif'
    },
    {
      name: 'Scheherazade',
      displayName: 'شهرزاد',
      preview: 'نص تجريبي بخط شهرزاد',
      category: 'serif'
    },
    {
      name: '<PERSON>o Sans Arabic',
      displayName: 'نوتو سانس عربي',
      preview: 'نص تجريبي بخط نوتو سانس',
      category: 'sans-serif'
    },
    {
      name: 'IBM Plex Sans Arabic',
      displayName: 'آي بي إم بلكس',
      preview: 'نص تجريبي بخط آي بي إم',
      category: 'sans-serif'
    },
    {
      name: 'Almarai',
      displayName: 'المرعي',
      preview: 'نص تجريبي بخط المرعي',
      category: 'sans-serif'
    },
    {
      name: 'Changa',
      displayName: 'تشانغا',
      preview: 'نص تجريبي بخط تشانغا',
      category: 'sans-serif'
    },
    {
      name: 'El Messiri',
      displayName: 'المسيري',
      preview: 'نص تجريبي بخط المسيري',
      category: 'sans-serif'
    },
    {
      name: 'Harmattan',
      displayName: 'هرمتان',
      preview: 'نص تجريبي بخط هرمتان',
      category: 'sans-serif'
    },
    {
      name: 'Katibeh',
      displayName: 'كاتبة',
      preview: 'نص تجريبي بخط كاتبة',
      category: 'display'
    },
    {
      name: 'Lalezar',
      displayName: 'لالزار',
      preview: 'نص تجريبي بخط لالزار',
      category: 'display'
    },
    {
      name: 'Lemonada',
      displayName: 'ليمونادا',
      preview: 'نص تجريبي بخط ليمونادا',
      category: 'display'
    },
    {
      name: 'Mada',
      displayName: 'مدى',
      preview: 'نص تجريبي بخط مدى',
      category: 'sans-serif'
    },
    {
      name: 'Markazi Text',
      displayName: 'نص مركزي',
      preview: 'نص تجريبي بخط مركزي',
      category: 'serif'
    },
    {
      name: 'Mirza',
      displayName: 'ميرزا',
      preview: 'نص تجريبي بخط ميرزا',
      category: 'display'
    },
    {
      name: 'Rakkas',
      displayName: 'رقاص',
      preview: 'نص تجريبي بخط رقاص',
      category: 'display'
    },
    {
      name: 'Reem Kufi',
      displayName: 'ريم كوفي',
      preview: 'نص تجريبي بخط ريم كوفي',
      category: 'sans-serif'
    },
    {
      name: 'Vibes',
      displayName: 'فايبز',
      preview: 'نص تجريبي بخط فايبز',
      category: 'display'
    }
  ];

  const handleFontSelect = (font) => {
    onFontChange(font.name);
    setIsOpen(false);
  };

  const selectedFontData = arabicFonts.find(font => font.name === selectedFont) || arabicFonts[0];

  return (
    <div className={`relative ${className}`}>
      {/* زر اختيار الخط */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-4 py-3 bg-white border border-gray-300 rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-200"
      >
        <div className="flex items-center">
          <FaFont className="text-gray-500 ml-3" />
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900" style={{ fontFamily: selectedFont }}>
              {selectedFontData.displayName}
            </div>
            <div className="text-xs text-gray-500">
              {selectedFontData.category}
            </div>
          </div>
        </div>
        <svg
          className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* قائمة الخطوط */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          <div className="p-2">
            <div className="text-xs font-medium text-gray-500 px-3 py-2 border-b border-gray-200 mb-2">
              اختر الخط المناسب للمحتوى
            </div>
            
            {/* تجميع الخطوط حسب الفئة */}
            {['sans-serif', 'serif', 'display'].map(category => {
              const categoryFonts = arabicFonts.filter(font => font.category === category);
              const categoryNames = {
                'sans-serif': 'خطوط بسيطة',
                'serif': 'خطوط كلاسيكية',
                'display': 'خطوط زخرفية'
              };

              return (
                <div key={category} className="mb-4">
                  <div className="text-xs font-medium text-gray-600 px-3 py-1 bg-gray-50 rounded mb-2">
                    {categoryNames[category]}
                  </div>
                  
                  {categoryFonts.map((font) => (
                    <button
                      key={font.name}
                      type="button"
                      onClick={() => handleFontSelect(font)}
                      className={`w-full text-right px-3 py-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center justify-between ${
                        selectedFont === font.name ? 'bg-primary-50 border border-primary-200' : ''
                      }`}
                    >
                      <div className="flex-1">
                        <div 
                          className="text-sm font-medium text-gray-900 mb-1"
                          style={{ fontFamily: font.name }}
                        >
                          {font.displayName}
                        </div>
                        <div 
                          className="text-xs text-gray-600"
                          style={{ fontFamily: font.name }}
                        >
                          {font.preview}
                        </div>
                      </div>
                      
                      {selectedFont === font.name && (
                        <FaCheck className="text-primary-600 mr-2" />
                      )}
                    </button>
                  ))}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* خلفية شفافة لإغلاق القائمة */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default FontSelector;
