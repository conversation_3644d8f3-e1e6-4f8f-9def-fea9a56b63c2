import { useState, useEffect } from 'react';
import AdminLayout from '../../../components/admin/AdminLayout';
import {
  FaSave,
  FaCog,
  FaGlobe,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaFacebook,
  FaTwitter,
  FaLinkedin,
  FaInstagram,
  FaEye,
  FaEyeSlash
} from 'react-icons/fa';

export default function Settings() {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [showPassword, setShowPassword] = useState(false);
  const [settings, setSettings] = useState({
    // إعدادات عامة
    siteName: 'مكتب المحاماة للاستشارات القانونية',
    siteDescription: 'مكتب محاماة متخصص في تقديم الاستشارات القانونية والخدمات القانونية المتكاملة',
    siteKeywords: 'محاماة, استشارات قانونية, قانون, محامي, خدمات قانونية',
    
    // معلومات الاتصال
    phone: '+20 2 1234 5678',
    email: '<EMAIL>',
    address: 'القاهرة، جمهورية مصر العربية',
    coordinates: '30.038704, 31.343061',
    workingHours: 'السبت - الخميس: 9:00 ص - 6:00 م',
    
    // وسائل التواصل الاجتماعي
    facebook: '',
    twitter: '',
    linkedin: '',
    instagram: '',
    
    // إعدادات البريد الإلكتروني
    smtpHost: '',
    smtpPort: '587',
    smtpUser: '',
    smtpPassword: '',
    smtpSecure: true,
    
    // إعدادات أخرى
    maintenanceMode: false,
    allowRegistration: true,
    enableNotifications: true
  });

  const [message, setMessage] = useState('');

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      // هنا يمكن إضافة API call لجلب الإعدادات
      console.log('جلب الإعدادات...');
    } catch (error) {
      console.error('خطأ في جلب الإعدادات:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // هنا يمكن إضافة API call لحفظ الإعدادات
      console.log('حفظ الإعدادات:', settings);
      
      // محاكاة API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setMessage('تم حفظ الإعدادات بنجاح');
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error);
      setMessage('حدث خطأ في حفظ الإعدادات');
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'general', name: 'إعدادات عامة', icon: FaCog },
    { id: 'contact', name: 'معلومات الاتصال', icon: FaPhone },
    { id: 'social', name: 'وسائل التواصل', icon: FaGlobe },
    { id: 'email', name: 'إعدادات البريد', icon: FaEnvelope }
  ];

  return (
    <AdminLayout title="الإعدادات">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">إعدادات النظام</h1>
          <p className="text-gray-600">إدارة إعدادات الموقع والنظام</p>
        </div>

        {/* Success Message */}
        {message && (
          <div className="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
            {message}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Tabs */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 ${
                      activeTab === tab.id
                        ? 'bg-primary-100 text-primary-700 border border-primary-200'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <tab.icon className="ml-3 h-5 w-5" />
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            <form onSubmit={handleSubmit}>
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                {/* General Settings */}
                {activeTab === 'general' && (
                  <div className="space-y-6">
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">الإعدادات العامة</h2>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        اسم الموقع
                      </label>
                      <input
                        type="text"
                        name="siteName"
                        value={settings.siteName}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        وصف الموقع
                      </label>
                      <textarea
                        name="siteDescription"
                        value={settings.siteDescription}
                        onChange={handleInputChange}
                        rows="3"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        الكلمات المفتاحية
                      </label>
                      <input
                        type="text"
                        name="siteKeywords"
                        value={settings.siteKeywords}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="افصل بين الكلمات بفاصلة"
                      />
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          name="maintenanceMode"
                          checked={settings.maintenanceMode}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                        <label className="mr-2 block text-sm text-gray-900">
                          وضع الصيانة
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          name="enableNotifications"
                          checked={settings.enableNotifications}
                          onChange={handleInputChange}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                        <label className="mr-2 block text-sm text-gray-900">
                          تفعيل الإشعارات
                        </label>
                      </div>
                    </div>
                  </div>
                )}

                {/* Contact Settings */}
                {activeTab === 'contact' && (
                  <div className="space-y-6">
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">معلومات الاتصال</h2>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          رقم الهاتف
                        </label>
                        <div className="relative">
                          <FaPhone className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                          <input
                            type="tel"
                            name="phone"
                            value={settings.phone}
                            onChange={handleInputChange}
                            className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          البريد الإلكتروني
                        </label>
                        <div className="relative">
                          <FaEnvelope className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                          <input
                            type="email"
                            name="email"
                            value={settings.email}
                            onChange={handleInputChange}
                            className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                          />
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        العنوان
                      </label>
                      <div className="relative">
                        <FaMapMarkerAlt className="absolute right-3 top-4 text-gray-400" />
                        <textarea
                          name="address"
                          value={settings.address}
                          onChange={handleInputChange}
                          rows="2"
                          className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        الإحداثيات (خط العرض، خط الطول)
                      </label>
                      <input
                        type="text"
                        name="coordinates"
                        value={settings.coordinates}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="30.038704, 31.343061"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        ساعات العمل
                      </label>
                      <input
                        type="text"
                        name="workingHours"
                        value={settings.workingHours}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    </div>
                  </div>
                )}

                {/* Social Media Settings */}
                {activeTab === 'social' && (
                  <div className="space-y-6">
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">وسائل التواصل الاجتماعي</h2>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Facebook
                        </label>
                        <div className="relative">
                          <FaFacebook className="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-600" />
                          <input
                            type="url"
                            name="facebook"
                            value={settings.facebook}
                            onChange={handleInputChange}
                            className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                            placeholder="https://facebook.com/..."
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Twitter
                        </label>
                        <div className="relative">
                          <FaTwitter className="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-400" />
                          <input
                            type="url"
                            name="twitter"
                            value={settings.twitter}
                            onChange={handleInputChange}
                            className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                            placeholder="https://twitter.com/..."
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          LinkedIn
                        </label>
                        <div className="relative">
                          <FaLinkedin className="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-700" />
                          <input
                            type="url"
                            name="linkedin"
                            value={settings.linkedin}
                            onChange={handleInputChange}
                            className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                            placeholder="https://linkedin.com/..."
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Instagram
                        </label>
                        <div className="relative">
                          <FaInstagram className="absolute right-3 top-1/2 transform -translate-y-1/2 text-pink-600" />
                          <input
                            type="url"
                            name="instagram"
                            value={settings.instagram}
                            onChange={handleInputChange}
                            className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                            placeholder="https://instagram.com/..."
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Email Settings */}
                {activeTab === 'email' && (
                  <div className="space-y-6">
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">إعدادات البريد الإلكتروني</h2>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          خادم SMTP
                        </label>
                        <input
                          type="text"
                          name="smtpHost"
                          value={settings.smtpHost}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                          placeholder="smtp.gmail.com"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          منفذ SMTP
                        </label>
                        <input
                          type="number"
                          name="smtpPort"
                          value={settings.smtpPort}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          اسم المستخدم
                        </label>
                        <input
                          type="text"
                          name="smtpUser"
                          value={settings.smtpUser}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          كلمة المرور
                        </label>
                        <div className="relative">
                          <input
                            type={showPassword ? 'text' : 'password'}
                            name="smtpPassword"
                            value={settings.smtpPassword}
                            onChange={handleInputChange}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          >
                            {showPassword ? <FaEyeSlash /> : <FaEye />}
                          </button>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        name="smtpSecure"
                        checked={settings.smtpSecure}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                      <label className="mr-2 block text-sm text-gray-900">
                        استخدام اتصال آمن (SSL/TLS)
                      </label>
                    </div>
                  </div>
                )}

                {/* Submit Button */}
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <button
                    type="submit"
                    disabled={loading}
                    className={`bg-primary-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center ${
                      loading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-primary-700'
                    }`}
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
                        جاري الحفظ...
                      </>
                    ) : (
                      <>
                        <FaSave className="ml-2" />
                        حفظ الإعدادات
                      </>
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
