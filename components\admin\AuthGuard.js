import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Fa<PERSON><PERSON><PERSON>, FaExclamationTriangle } from 'react-icons/fa';

const AuthGuard = ({ children, requireAdmin = true }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [user, setUser] = useState(null);
  const router = useRouter();

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      
      if (!token) {
        redirectToLogin();
        return;
      }

      // التحقق من صحة التوكن مع الخادم
      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        
        if (data.success) {
          // التحقق من الصلاحيات إذا كانت مطلوبة
          if (requireAdmin && data.user.role !== 'admin') {
            setError('ليس لديك صلاحية للوصول إلى هذه الصفحة');
            setIsLoading(false);
            return;
          }

          setUser(data.user);
          setIsAuthenticated(true);
          
          // تحديث بيانات المستخدم في localStorage
          localStorage.setItem('adminUser', JSON.stringify(data.user));
        } else {
          throw new Error(data.message);
        }
      } else {
        throw new Error('فشل في التحقق من المصادقة');
      }
    } catch (error) {
      console.error('Authentication check failed:', error);
      
      // إزالة البيانات غير الصالحة
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminUser');
      
      redirectToLogin();
    } finally {
      setIsLoading(false);
    }
  };

  const redirectToLogin = () => {
    const currentPath = router.asPath;
    const loginUrl = `/admin/login${currentPath !== '/admin' ? `?redirect=${encodeURIComponent(currentPath)}` : ''}`;
    router.push(loginUrl);
  };

  // شاشة التحميل
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center">
        <div className="text-center">
          <FaSpinner className="animate-spin text-4xl text-primary-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-primary-800 mb-2">جاري التحقق من المصادقة</h2>
          <p className="text-primary-600">يرجى الانتظار...</p>
        </div>
      </div>
    );
  }

  // شاشة الخطأ
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <FaExclamationTriangle className="text-5xl text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-red-800 mb-4">خطأ في الوصول</h2>
          <p className="text-red-600 mb-6">{error}</p>
          <div className="space-y-3">
            <button
              onClick={() => router.push('/admin/login')}
              className="w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              تسجيل الدخول
            </button>
            <button
              onClick={() => router.push('/')}
              className="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              العودة للموقع الرئيسي
            </button>
          </div>
        </div>
      </div>
    );
  }

  // إذا كان المستخدم مصادق عليه، عرض المحتوى
  if (isAuthenticated) {
    return children;
  }

  // في حالة عدم المصادقة (لن يصل هنا عادة بسبب التوجيه)
  return null;
};

export default AuthGuard;
