import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '../../../../components/admin/AdminLayout';
import WYSIWYGEditor from '../../../../components/admin/WYSIWYGEditor';
import Link from 'next/link';
import {
  FaSave,
  FaArrowLeft,
  FaImage,
  FaUpload,
  FaStar,
  FaEye,
  FaTimes,
  FaCheck,
  FaExclamationTriangle,
  FaInfoCircle,
  FaUser,
  FaTag,
  FaCalendar,
  FaGlobe,
  FaClock,
  FaFileAlt,
  FaCog,
  FaFont,
  FaGavel,
  FaSort,
  FaArrowRight
} from 'react-icons/fa';

export default function EditExpertiseArea() {
  const router = useRouter();
  const { id } = router.query;
  const fileInputRef = useRef(null);

  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    description: '',
    content: '',
    icon: '⚖️',
    image: '',
    status: 'draft',
    featured: false,
    order: 0,
    redirect_to_services: false,
    services_category: '',
    meta_title: '',
    meta_description: ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [imagePreview, setImagePreview] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});
  const [savedDraft, setSavedDraft] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [imageUploading, setImageUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [serviceCategories, setServiceCategories] = useState([]);
  const [initialLoading, setInitialLoading] = useState(true);

  // Fetch expertise area data
  useEffect(() => {
    if (id) {
      fetchExpertiseArea();
      fetchServiceCategories();
    }
  }, [id]);

  const fetchExpertiseArea = async () => {
    try {
      setInitialLoading(true);
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/expertise/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (data.success) {
        setFormData(data.expertiseArea);
        if (data.expertiseArea.image) {
          setImagePreview(data.expertiseArea.image);
        }
      } else {
        setError(data.message || 'خطأ في جلب بيانات مجال الخبرة');
      }
    } catch (error) {
      console.error('Error fetching expertise area:', error);
      setError('حدث خطأ في جلب بيانات مجال الخبرة');
    } finally {
      setInitialLoading(false);
    }
  };

  const fetchServiceCategories = async () => {
    try {
      const response = await fetch('/api/admin/service-categories');
      const data = await response.json();
      if (data.success) {
        setServiceCategories(data.categories);
      }
    } catch (error) {
      console.error('Error fetching service categories:', error);
    }
  };

  // Auto-save functionality
  useEffect(() => {
    if (autoSaveEnabled && (formData.title || formData.content) && !initialLoading) {
      const timer = setTimeout(() => {
        saveDraft();
      }, 30000); // Auto-save every 30 seconds

      return () => {
        clearTimeout(timer);
      };
    }
  }, [formData, autoSaveEnabled, initialLoading]);

  const validateField = (name, value) => {
    const errors = { ...validationErrors };

    switch (name) {
      case 'title':
        if (!value.trim()) {
          errors.title = 'العنوان مطلوب';
        } else if (value.length < 3) {
          errors.title = 'العنوان يجب أن يكون 3 أحرف على الأقل';
        } else if (value.length > 100) {
          errors.title = 'العنوان يجب أن يكون أقل من 100 حرف';
        } else {
          delete errors.title;
        }
        break;
      case 'content':
        const textContent = value.replace(/<[^>]*>/g, '');
        if (!textContent.trim()) {
          errors.content = 'المحتوى مطلوب';
        } else if (textContent.length < 50) {
          errors.content = 'المحتوى يجب أن يكون 50 حرف على الأقل';
        } else {
          delete errors.content;
        }
        break;
      case 'description':
        if (!value.trim()) {
          errors.description = 'الوصف مطلوب';
        } else if (value.length > 300) {
          errors.description = 'الوصف يجب أن يكون أقل من 300 حرف';
        } else {
          delete errors.description;
        }
        break;
      default:
        break;
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;

    setFormData(prev => ({
      ...prev,
      [name]: newValue
    }));

    setError('');
    setSuccess('');

    // Validate field
    validateField(name, newValue);

    // Auto-generate slug from title
    if (name === 'title') {
      const slug = value.toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setFormData(prev => ({
        ...prev,
        slug: slug,
        meta_title: value // Auto-fill meta title
      }));
    }

    // Auto-fill meta description from description
    if (name === 'description') {
      setFormData(prev => ({
        ...prev,
        meta_description: value
      }));
    }
  };

  const handleContentChange = (content) => {
    setFormData(prev => ({
      ...prev,
      content
    }));
    validateField('content', content);
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
      setError('يُسمح فقط بملفات الصور');
      return;
    }

    // التحقق من حجم الملف (5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('حجم الصورة يجب أن يكون أقل من 5 ميجابايت');
      return;
    }

    setImageUploading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        setFormData(prev => ({
          ...prev,
          image: result.image.url
        }));
        setImagePreview(result.image.url);
        setSuccess('تم رفع الصورة بنجاح');
      } else {
        setError(result.error || 'خطأ في رفع الصورة');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setError('خطأ في رفع الصورة');
    } finally {
      setImageUploading(false);
    }
  };

  const removeImage = () => {
    setFormData(prev => ({
      ...prev,
      image: ''
    }));
    setImagePreview('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const saveDraft = async () => {
    try {
      localStorage.setItem(`expertiseDraft_${id}`, JSON.stringify(formData));
      setSavedDraft(true);
      setTimeout(() => setSavedDraft(false), 2000);
    } catch (error) {
      console.error('خطأ في حفظ المسودة:', error);
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.title.trim()) errors.title = 'العنوان مطلوب';
    if (!formData.content.trim()) errors.content = 'المحتوى مطلوب';
    if (!formData.description.trim()) errors.description = 'الوصف مطلوب';
    if (formData.title.length < 3) errors.title = 'العنوان قصير جداً';

    const textContent = formData.content.replace(/<[^>]*>/g, '');
    if (textContent.length < 50) errors.content = 'المحتوى قصير جداً';

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e, shouldRedirect = true) => {
    if (e && e.preventDefault) {
      e.preventDefault();
    }

    // منع الإرسال المتعدد
    if (isSubmitting) {
      return;
    }

    if (!validateForm()) {
      setError('يرجى تصحيح الأخطاء قبل الحفظ');
      return;
    }

    setIsSubmitting(true);
    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/expertise/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('تم تحديث مجال الخبرة بنجاح');
        localStorage.removeItem(`expertiseDraft_${id}`);

        if (shouldRedirect) {
          setTimeout(() => {
            router.push('/admin/expertise');
          }, 1500);
        }
      } else {
        setError(result.message || 'حدث خطأ في تحديث مجال الخبرة');
      }
    } catch (error) {
      console.error('Submit error:', error);
      setError('حدث خطأ في تحديث مجال الخبرة');
    } finally {
      setLoading(false);
      setIsSubmitting(false);
    }
  };

  const getProgressPercentage = () => {
    let completed = 0;
    const total = 6;

    if (formData.title) completed++;
    if (formData.content) completed++;
    if (formData.description) completed++;
    if (formData.icon) completed++;
    if (formData.image) completed++;
    if (formData.order) completed++;

    return Math.round((completed / total) * 100);
  };

  if (initialLoading) {
    return (
      <AdminLayout title="تحديث مجال الخبرة">
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل بيانات مجال الخبرة...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (error && !formData.title) {
    return (
      <AdminLayout title="خطأ">
        <div className="text-center py-12">
          <div className="text-6xl text-gray-400 mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">خطأ في تحميل البيانات</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link href="/admin/expertise">
            <button className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors">
              العودة إلى قائمة مجالات الخبرة
            </button>
          </Link>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="تحديث مجال الخبرة">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl shadow-lg text-white p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex-1">
              <h1 className="text-2xl lg:text-3xl font-bold mb-2">⚖️ تحديث مجال الخبرة القانونية</h1>
              <p className="text-primary-100">تعديل وتحديث مجال الخبرة القانونية</p>
            </div>

            <div className="flex items-center gap-3">
              {/* Progress Indicator */}
              <div className="hidden lg:flex items-center gap-3 bg-white/10 rounded-lg px-4 py-2">
                <FaGavel className="text-primary-200" />
                <div className="text-sm">
                  <div className="flex items-center gap-2 mb-1">
                    <span>التقدم:</span>
                    <span className="font-bold">{getProgressPercentage()}%</span>
                  </div>
                  <div className="w-24 bg-white/20 rounded-full h-1">
                    <div
                      className="bg-white h-1 rounded-full transition-all duration-300"
                      style={{ width: `${getProgressPercentage()}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              {/* Auto-save indicator */}
              <div className="flex items-center gap-2 bg-white/10 rounded-lg px-3 py-2">
                <FaClock className="text-primary-200" />
                <span className="text-sm">
                  {savedDraft ? 'تم الحفظ!' : 'حفظ تلقائي'}
                </span>
              </div>

              {/* Actions */}
              <button
                onClick={saveDraft}
                className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center text-sm"
              >
                <FaSave className="ml-2" />
                حفظ مسودة
              </button>

              <Link href="/admin/expertise">
                <button className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center text-sm">
                  <FaArrowLeft className="ml-2" />
                  العودة
                </button>
              </Link>
            </div>
          </div>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 flex items-start">
            <FaExclamationTriangle className="text-red-500 ml-3 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-red-800 font-medium">خطأ</h4>
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-4 flex items-start">
            <FaCheck className="text-green-500 ml-3 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-green-800 font-medium">تم بنجاح</h4>
              <p className="text-green-700 text-sm">{success}</p>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Form Content */}
          <div className="lg:col-span-3">
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-primary-100 rounded-xl flex items-center justify-center ml-3">
                    <span className="text-primary-600 font-bold text-lg">1</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">المعلومات الأساسية</h3>
                    <p className="text-sm text-gray-600">العنوان والوصف والرابط المختصر</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <FaGavel className="inline ml-1" />
                      عنوان مجال الخبرة *
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                        validationErrors.title ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="مثال: القانون التجاري والشركات"
                    />
                    {validationErrors.title && (
                      <p className="text-red-500 text-sm mt-1">{validationErrors.title}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <FaGlobe className="inline ml-1" />
                      الرابط المختصر
                    </label>
                    <input
                      type="text"
                      name="slug"
                      value={formData.slug}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="commercial-law"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <FaFont className="inline ml-1" />
                      الأيقونة
                    </label>
                    <input
                      type="text"
                      name="icon"
                      value={formData.icon}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="⚖️"
                    />
                    <p className="text-xs text-gray-500 mt-1">استخدم رمز تعبيري (emoji) أو أيقونة</p>
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <FaInfoCircle className="inline ml-1" />
                      وصف مجال الخبرة *
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={3}
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                        validationErrors.description ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="وصف مختصر يوضح ما يشمله هذا المجال من خدمات قانونية..."
                    />
                    {validationErrors.description && (
                      <p className="text-red-500 text-sm mt-1">{validationErrors.description}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Submit Buttons */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex flex-col sm:flex-row gap-4 justify-end">
                  <button
                    type="button"
                    onClick={() => {
                      setFormData(prev => ({ ...prev, status: 'draft' }));
                      handleSubmit(null, false);
                    }}
                    disabled={loading}
                    className="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors disabled:opacity-50 flex items-center justify-center"
                  >
                    <FaSave className="ml-2" />
                    حفظ كمسودة
                  </button>

                  <button
                    type="button"
                    onClick={() => {
                      setFormData(prev => ({ ...prev, status: 'published' }));
                      handleSubmit();
                    }}
                    disabled={loading}
                    className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 flex items-center justify-center"
                  >
                    {loading ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
                    ) : (
                      <FaCheck className="ml-2" />
                    )}
                    تحديث مجال الخبرة
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
