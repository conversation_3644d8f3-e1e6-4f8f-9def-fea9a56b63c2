import { useState, useEffect } from 'react';
import AdminLayout from '../../../components/admin/AdminLayout';
import DateDisplay from '../../../components/DateDisplay';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaTags,
  FaSave,
  FaTimes,
  FaNewspaper,
  FaConciergeBell
} from 'react-icons/fa';

export default function CategoriesManagement() {
  const [activeTab, setActiveTab] = useState('articles'); // 'articles' or 'services'
  const [categories, setCategories] = useState([]);
  const [serviceCategories, setServiceCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    color: '#3B82F6',
    type: 'articles' // 'articles' or 'services'
  });

  useEffect(() => {
    fetchCategories();
    fetchServiceCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/categories');
      const data = await response.json();

      if (data.success) {
        setCategories(data.categories);
      } else {
        setError(data.message);
      }
    } catch (error) {
      setError('حدث خطأ في جلب تصنيفات المقالات');
    } finally {
      setLoading(false);
    }
  };

  const fetchServiceCategories = async () => {
    try {
      const response = await fetch('/api/admin/service-categories');
      const data = await response.json();

      if (data.success) {
        setServiceCategories(data.categories);
      } else {
        console.error('خطأ في جلب تصنيفات الخدمات:', data.message);
      }
    } catch (error) {
      console.error('حدث خطأ في جلب تصنيفات الخدمات:', error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    try {
      const isServiceCategory = formData.type === 'services';
      const baseUrl = isServiceCategory ? '/api/admin/service-categories' : '/api/admin/categories';

      const url = editingCategory
        ? `${baseUrl}/${editingCategory.id}`
        : baseUrl;

      const method = editingCategory ? 'PUT' : 'POST';

      const token = localStorage.getItem('adminToken');
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` })
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        setSuccess(data.message);
        setShowModal(false);
        setEditingCategory(null);
        setFormData({ name: '', slug: '', description: '', color: '#3B82F6', type: 'articles' });

        // تحديث التصنيفات المناسبة
        if (isServiceCategory) {
          fetchServiceCategories();
        } else {
          fetchCategories();
        }
      } else {
        setError(data.message);
      }
    } catch (error) {
      setError('حدث خطأ في حفظ التصنيف');
    }
  };

  const handleEdit = (category, isServiceCategory = false) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      slug: category.slug,
      description: category.description,
      color: category.color,
      type: isServiceCategory ? 'services' : 'articles'
    });
    setShowModal(true);
  };

  const handleDelete = async (id, isServiceCategory = false) => {
    if (!confirm('هل أنت متأكد من حذف هذا التصنيف؟')) return;

    try {
      const baseUrl = isServiceCategory ? '/api/admin/service-categories' : '/api/admin/categories';
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`${baseUrl}/${id}`, {
        method: 'DELETE',
        headers: {
          ...(token && { 'Authorization': `Bearer ${token}` })
        }
      });
      const data = await response.json();

      if (data.success) {
        setSuccess('تم حذف التصنيف بنجاح');
        if (isServiceCategory) {
          fetchServiceCategories();
        } else {
          fetchCategories();
        }
      } else {
        setError(data.message);
      }
    } catch (error) {
      setError('حدث خطأ في حذف التصنيف');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // إنشاء slug تلقائياً من الاسم
    if (name === 'name') {
      const slug = value.toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-');
      setFormData(prev => ({ ...prev, slug }));
    }
  };

  const closeModal = () => {
    setShowModal(false);
    setEditingCategory(null);
    setFormData({ name: '', slug: '', description: '', color: '#3B82F6', type: activeTab });
    setError('');
  };

  const openAddModal = (type = 'articles') => {
    setFormData({ name: '', slug: '', description: '', color: '#3B82F6', type });
    setShowModal(true);
  };

  return (
    <AdminLayout title="إدارة التصنيفات">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة التصنيفات</h1>
            <p className="text-gray-600">إدارة تصنيفات المقالات والخدمات الإلكترونية</p>
          </div>
          <button
            onClick={() => openAddModal(activeTab)}
            className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors flex items-center gap-2"
          >
            <FaPlus />
            إضافة تصنيف جديد
          </button>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('articles')}
                className={`py-4 px-6 text-sm font-medium border-b-2 transition-colors flex items-center gap-2 ${
                  activeTab === 'articles'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <FaNewspaper />
                تصنيفات المقالات والأخبار
                <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                  {categories.length}
                </span>
              </button>
              <button
                onClick={() => setActiveTab('services')}
                className={`py-4 px-6 text-sm font-medium border-b-2 transition-colors flex items-center gap-2 ${
                  activeTab === 'services'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <FaConciergeBell />
                تصنيفات الخدمات الإلكترونية
                <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                  {serviceCategories.length}
                </span>
              </button>
            </nav>
          </div>
        </div>

        {/* Alerts */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            {error}
          </div>
        )}
        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
            {success}
          </div>
        )}

        {/* Categories Grid */}
        <div className="bg-white rounded-lg shadow">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">جاري التحميل...</p>
            </div>
          ) : (() => {
            const currentCategories = activeTab === 'articles' ? categories : serviceCategories;
            const categoryType = activeTab === 'articles' ? 'المقالات' : 'الخدمات الإلكترونية';

            return currentCategories.length === 0 ? (
              <div className="text-center py-12">
                <FaTags className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد تصنيفات {categoryType}</h3>
                <p className="mt-1 text-sm text-gray-500">ابدأ بإضافة تصنيف جديد</p>
                <div className="mt-6">
                  <button
                    onClick={() => openAddModal(activeTab)}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                  >
                    <FaPlus className="ml-2" />
                    إضافة تصنيف جديد
                  </button>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                {currentCategories.map((category) => (
                  <div key={category.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: category.color }}
                        ></div>
                        {activeTab === 'services' && (
                          <FaConciergeBell className="text-primary-600 text-sm" />
                        )}
                        {activeTab === 'articles' && (
                          <FaNewspaper className="text-primary-600 text-sm" />
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleEdit(category, activeTab === 'services')}
                          className="text-blue-600 hover:text-blue-900"
                          title="تعديل"
                        >
                          <FaEdit />
                        </button>
                        <button
                          onClick={() => handleDelete(category.id, activeTab === 'services')}
                          className="text-red-600 hover:text-red-900"
                          title="حذف"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </div>

                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {category.name}
                    </h3>

                    {category.description && (
                      <p className="text-gray-600 text-sm mb-3">
                        {category.description}
                      </p>
                    )}

                    <div className="text-xs text-gray-500">
                      <p>الرابط: {category.slug}</p>
                      <p>تاريخ الإنشاء: <DateDisplay dateString={category.created_at} /></p>
                    </div>
                  </div>
                ))}
              </div>
            );
          })()}
        </div>

        {/* Modal */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                  {formData.type === 'services' ? <FaConciergeBell /> : <FaNewspaper />}
                  {editingCategory ? 'تعديل التصنيف' : 'إضافة تصنيف جديد'}
                  <span className="text-sm font-normal text-gray-500">
                    ({formData.type === 'services' ? 'الخدمات الإلكترونية' : 'المقالات والأخبار'})
                  </span>
                </h2>
                <button
                  onClick={closeModal}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FaTimes />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع التصنيف *
                  </label>
                  <select
                    name="type"
                    value={formData.type}
                    onChange={handleInputChange}
                    disabled={editingCategory} // منع تغيير النوع عند التعديل
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:bg-gray-100"
                  >
                    <option value="articles">تصنيفات المقالات والأخبار</option>
                    <option value="services">تصنيفات الخدمات الإلكترونية</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    اسم التصنيف *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="أدخل اسم التصنيف"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الرابط المختصر
                  </label>
                  <input
                    type="text"
                    name="slug"
                    value={formData.slug}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="سيتم إنشاؤه تلقائياً"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الوصف
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="وصف التصنيف (اختياري)"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    اللون
                  </label>
                  <input
                    type="color"
                    name="color"
                    value={formData.color}
                    onChange={handleInputChange}
                    className="w-full h-10 border border-gray-300 rounded-lg"
                  />
                </div>

                <div className="flex justify-end gap-3 pt-4">
                  <button
                    type="button"
                    onClick={closeModal}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    type="submit"
                    className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                  >
                    <FaSave />
                    {editingCategory ? 'تحديث' : 'حفظ'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
