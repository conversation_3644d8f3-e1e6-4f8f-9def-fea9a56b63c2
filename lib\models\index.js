// تصدير جميع النماذج من مكان واحد
export { default as User } from './User.js';
export { default as Article } from './Article.js';
export { default as Service } from './Service.js';
export { default as Category } from './Category.js';
export { default as Slide } from './Slide.js';
export { default as Page } from './Page.js';
export { default as ContactMessage } from './ContactMessage.js';
export { default as TeamMember } from './TeamMember.js';
export { default as ElectronicService } from './ElectronicService.js';
export { default as Expertise } from './Expertise.js';
export { default as Setting } from './Setting.js';
export { default as AboutUs } from './AboutUs.js';

// دالة لتهيئة جميع النماذج
export async function initializeModels() {
  try {
    // استيراد جميع النماذج لضمان تسجيلها
    const models = await Promise.all([
      import('./User.js'),
      import('./Article.js'),
      import('./Service.js'),
      import('./Category.js'),
      import('./Slide.js'),
      import('./Page.js'),
      import('./ContactMessage.js'),
      import('./TeamMember.js'),
      import('./ElectronicService.js'),
      import('./Expertise.js'),
      import('./Setting.js'),
      import('./AboutUs.js')
    ]);

    console.log('All models initialized successfully');
    return models;
  } catch (error) {
    console.error('Error initializing models:', error);
    throw error;
  }
}
