import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import {
  FaBars,
  FaTimes,
  FaTachometerAlt,
  FaImages,
  FaNewspaper,
  FaCog,
  FaUsers,
  FaConciergeBell,
  FaSignOutAlt,
  FaUser,
  FaEnvelope,
  FaFileAlt,
  FaGavel,
  FaLaptop,
  FaTags
} from 'react-icons/fa';

const SimpleAdminLayout = ({ children, title = 'لوحة التحكم' }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      const userData = localStorage.getItem('adminUser');

      if (!token) {
        router.push('/admin/login');
        return;
      }

      if (userData) {
        try {
          setUser(JSON.parse(userData));
        } catch (error) {
          console.error('خطأ في تحليل بيانات المستخدم:', error);
          localStorage.removeItem('adminUser');
          router.push('/admin/login');
          return;
        }
      }

      // التحقق من صحة التوكن مع الخادم (اختياري)
      try {
        const response = await fetch('/api/auth/me', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          localStorage.removeItem('adminToken');
          localStorage.removeItem('adminUser');
          router.push('/admin/login');
          return;
        }
      } catch (error) {
        console.error('فشل التحقق من التوكن:', error);
      }

    } catch (error) {
      console.error('خطأ في التحقق من المصادقة:', error);
      router.push('/admin/login');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    if (!confirm('هل أنت متأكد من تسجيل الخروج؟')) {
      return;
    }

    try {
      const token = localStorage.getItem('adminToken');
      
      if (token) {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
      }
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
    } finally {
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminUser');
      localStorage.removeItem('rememberedEmail');
      
      window.location.href = '/admin/login-simple';
    }
  };

  const navigation = [
    {
      name: 'لوحة التحكم',
      href: '/admin',
      icon: FaTachometerAlt,
      current: router.pathname === '/admin'
    },
    {
      name: 'المقالات',
      href: '/admin/articles',
      icon: FaFileAlt,
      current: router.pathname.startsWith('/admin/articles')
    },
    {
      name: 'الأخبار',
      href: '/admin/news',
      icon: FaNewspaper,
      current: router.pathname.startsWith('/admin/news')
    },
    {
      name: 'الخدمات',
      href: '/admin/services',
      icon: FaConciergeBell,
      current: router.pathname.startsWith('/admin/services')
    },
    {
      name: 'الخدمات الإلكترونية',
      href: '/admin/electronic-services',
      icon: FaLaptop,
      current: router.pathname.startsWith('/admin/electronic-services')
    },
    {
      name: 'التصنيفات',
      href: '/admin/categories',
      icon: FaTags,
      current: router.pathname.startsWith('/admin/categories')
    },
    {
      name: 'المعرض',
      href: '/admin/gallery',
      icon: FaImages,
      current: router.pathname.startsWith('/admin/gallery')
    },
    {
      name: 'الرسائل',
      href: '/admin/messages',
      icon: FaEnvelope,
      current: router.pathname.startsWith('/admin/messages')
    },
    {
      name: 'المستخدمون',
      href: '/admin/users',
      icon: FaUsers,
      current: router.pathname.startsWith('/admin/users')
    },
    {
      name: 'الإعدادات',
      href: '/admin/settings',
      icon: FaCog,
      current: router.pathname.startsWith('/admin/settings')
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-primary-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-primary-700 font-medium">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>{title} - لوحة التحكم الإدارية</title>
        <meta name="robots" content="noindex, nofollow" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-gray-50 flex">
        {/* Sidebar */}
        <div className={`${sidebarOpen ? 'translate-x-0' : 'translate-x-full'} fixed inset-y-0 right-0 z-50 w-64 bg-gradient-to-b from-primary-900 to-primary-800 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}>
          <div className="flex items-center justify-between h-16 px-4 bg-primary-800">
            <h1 className="text-white text-lg font-bold">لوحة التحكم</h1>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden text-white hover:text-gray-300"
            >
              <FaTimes className="h-6 w-6" />
            </button>
          </div>

          {/* User Info */}
          <div className="p-4 border-b border-primary-800/50">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-r from-accent-400 to-accent-500 rounded-full flex items-center justify-center shadow-lg">
                <FaUser className="text-white text-lg" />
              </div>
              <div className="mr-3">
                <p className="text-white font-semibold">{user?.username || 'المدير'}</p>
                <p className="text-primary-300 text-sm flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full ml-2"></span>
                  {user?.role === 'admin' ? 'مدير النظام' : 'مستخدم'}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="mt-4 px-4">
            <ul className="space-y-2">
              {navigation.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={`group flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 ${
                      item.current
                        ? 'bg-primary-700 text-white shadow-lg'
                        : 'text-primary-100 hover:bg-primary-700 hover:text-white'
                    }`}
                  >
                    <item.icon className="ml-3 h-5 w-5" />
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>

          {/* Logout Button */}
          <div className="absolute bottom-4 left-4 right-4">
            <button
              onClick={handleLogout}
              className="w-full flex items-center px-4 py-3 text-sm font-medium text-primary-100 hover:bg-red-600 hover:text-white rounded-lg transition-colors duration-200"
            >
              <FaSignOutAlt className="ml-3 h-5 w-5" />
              تسجيل الخروج
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Top Bar */}
          <header className="bg-white shadow-sm border-b border-gray-200">
            <div className="flex items-center justify-between px-4 py-4">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden text-gray-500 hover:text-gray-700"
              >
                <FaBars className="h-6 w-6" />
              </button>
              <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">مرحباً، {user?.username}</span>
              </div>
            </div>
          </header>

          {/* Page Content */}
          <main className="flex-1 overflow-y-auto p-6">
            {children}
          </main>
        </div>

        {/* Overlay */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </div>
    </>
  );
};

export default SimpleAdminLayout;
