// تحسينات SEO لـ Next.js 15

// إعدادات SEO الافتراضية
export const defaultSEO = {
  title: 'مكتب المحاماة - استشارات قانونية متخصصة',
  description: 'نقدم خدمات قانونية شاملة ومتخصصة في جميع فروع القانون مع فريق من المحامين ذوي الخبرة العالية',
  canonical: 'https://lawfirm.com',
  openGraph: {
    type: 'website',
    locale: 'ar_SA',
    url: 'https://lawfirm.com',
    siteName: 'مكتب المحاماة',
    title: 'مكتب المحاماة - استشارات قانونية متخصصة',
    description: 'نقدم خدمات قانونية شاملة ومتخصصة في جميع فروع القانون مع فريق من المحامين ذوي الخبرة العالية',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'مكتب المحاماة',
      },
    ],
  },
  twitter: {
    handle: '@lawfirm',
    site: '@lawfirm',
    cardType: 'summary_large_image',
  },
  additionalMetaTags: [
    {
      name: 'viewport',
      content: 'width=device-width, initial-scale=1',
    },
    {
      name: 'robots',
      content: 'index,follow',
    },
    {
      name: 'googlebot',
      content: 'index,follow',
    },
    {
      httpEquiv: 'x-ua-compatible',
      content: 'IE=edge',
    },
  ],
  additionalLinkTags: [
    {
      rel: 'icon',
      href: '/favicon.ico',
    },
    {
      rel: 'apple-touch-icon',
      href: '/apple-touch-icon.png',
      sizes: '180x180',
    },
    {
      rel: 'manifest',
      href: '/manifest.json',
    },
  ],
};

// دالة لإنشاء SEO للصفحات
export function generatePageSEO({
  title,
  description,
  canonical,
  images,
  noindex = false,
  nofollow = false,
}) {
  const fullTitle = title ? `${title} - مكتب المحاماة` : defaultSEO.title;
  const fullDescription = description || defaultSEO.description;
  const fullCanonical = canonical || defaultSEO.canonical;
  
  const robots = [];
  if (noindex) robots.push('noindex');
  else robots.push('index');
  if (nofollow) robots.push('nofollow');
  else robots.push('follow');
  
  return {
    title: fullTitle,
    description: fullDescription,
    canonical: fullCanonical,
    openGraph: {
      ...defaultSEO.openGraph,
      title: fullTitle,
      description: fullDescription,
      url: fullCanonical,
      images: images || defaultSEO.openGraph.images,
    },
    twitter: {
      ...defaultSEO.twitter,
      title: fullTitle,
      description: fullDescription,
    },
    additionalMetaTags: [
      ...defaultSEO.additionalMetaTags,
      {
        name: 'robots',
        content: robots.join(','),
      },
    ],
  };
}

// دالة لإنشاء SEO للمقالات
export function generateArticleSEO({
  title,
  description,
  canonical,
  publishedTime,
  modifiedTime,
  author,
  tags,
  images,
}) {
  const fullTitle = `${title} - مكتب المحاماة`;
  
  return {
    title: fullTitle,
    description,
    canonical,
    openGraph: {
      type: 'article',
      locale: 'ar_SA',
      url: canonical,
      siteName: 'مكتب المحاماة',
      title: fullTitle,
      description,
      images: images || defaultSEO.openGraph.images,
      article: {
        publishedTime,
        modifiedTime,
        author: [author],
        tags,
      },
    },
    twitter: {
      ...defaultSEO.twitter,
      title: fullTitle,
      description,
    },
    additionalMetaTags: [
      ...defaultSEO.additionalMetaTags,
      {
        name: 'article:author',
        content: author,
      },
      {
        name: 'article:published_time',
        content: publishedTime,
      },
      {
        name: 'article:modified_time',
        content: modifiedTime,
      },
      ...(tags || []).map(tag => ({
        name: 'article:tag',
        content: tag,
      })),
    ],
  };
}

// دالة لإنشاء JSON-LD Schema
export function generateJSONLD(type, data) {
  const baseSchema = {
    '@context': 'https://schema.org',
    '@type': type,
  };
  
  switch (type) {
    case 'LegalService':
      return {
        ...baseSchema,
        name: data.name || 'مكتب المحاماة',
        description: data.description,
        url: data.url,
        telephone: data.telephone,
        address: {
          '@type': 'PostalAddress',
          streetAddress: data.address?.street,
          addressLocality: data.address?.city,
          addressCountry: data.address?.country || 'SA',
        },
        openingHours: data.openingHours || 'Mo-Fr 09:00-17:00',
        priceRange: data.priceRange || '$$',
        areaServed: data.areaServed || 'Saudi Arabia',
      };
      
    case 'Article':
      return {
        ...baseSchema,
        headline: data.title,
        description: data.description,
        image: data.image,
        author: {
          '@type': 'Person',
          name: data.author,
        },
        publisher: {
          '@type': 'Organization',
          name: 'مكتب المحاماة',
          logo: {
            '@type': 'ImageObject',
            url: '/images/logo.png',
          },
        },
        datePublished: data.publishedTime,
        dateModified: data.modifiedTime,
        mainEntityOfPage: {
          '@type': 'WebPage',
          '@id': data.url,
        },
      };
      
    case 'Service':
      return {
        ...baseSchema,
        name: data.name,
        description: data.description,
        provider: {
          '@type': 'LegalService',
          name: 'مكتب المحاماة',
        },
        areaServed: 'Saudi Arabia',
        serviceType: data.serviceType,
      };
      
    case 'WebSite':
      return {
        ...baseSchema,
        name: 'مكتب المحاماة',
        url: 'https://lawfirm.com',
        potentialAction: {
          '@type': 'SearchAction',
          target: 'https://lawfirm.com/search?q={search_term_string}',
          'query-input': 'required name=search_term_string',
        },
      };
      
    default:
      return baseSchema;
  }
}

// دالة لإنشاء Breadcrumb Schema
export function generateBreadcrumbSchema(breadcrumbs) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url,
    })),
  };
}

// دالة لإنشاء FAQ Schema
export function generateFAQSchema(faqs) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };
}

// دالة لتحسين الصور للـ SEO
export function optimizeImageForSEO(src, alt, title) {
  return {
    src,
    alt: alt || title,
    title,
    loading: 'lazy',
    decoding: 'async',
  };
}

// دالة لإنشاء sitemap
export function generateSitemap(pages) {
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${pages.map(page => `
  <url>
    <loc>${page.url}</loc>
    <lastmod>${page.lastmod || new Date().toISOString()}</lastmod>
    <changefreq>${page.changefreq || 'weekly'}</changefreq>
    <priority>${page.priority || '0.8'}</priority>
  </url>
`).join('')}
</urlset>`;
  
  return sitemap;
}

// دالة لإنشاء robots.txt
export function generateRobotsTxt(siteUrl) {
  return `User-agent: *
Allow: /

User-agent: Googlebot
Allow: /

Disallow: /admin/
Disallow: /api/
Disallow: /_next/
Disallow: /uploads/temp/

Sitemap: ${siteUrl}/sitemap.xml`;
}

// تصدير جميع الدوال
export default {
  defaultSEO,
  generatePageSEO,
  generateArticleSEO,
  generateJSONLD,
  generateBreadcrumbSchema,
  generateFAQSchema,
  optimizeImageForSEO,
  generateSitemap,
  generateRobotsTxt,
};
