import { useState, useEffect } from 'react';
import AdminLayout from '../../../components/admin/AdminLayout';
import Link from 'next/link';
import DateDisplay from '../../../components/DateDisplay';
import { 
  FaPlus, 
  FaEdit, 
  FaTrash, 
  FaEye, 
  FaEyeSlash,
  FaStar,
  FaRegStar,
  FaLaptop,
  FaSearch,
  FaFilter
} from 'react-icons/fa';

export default function ElectronicServicesManagement() {
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    category: '',
    page: 1,
    limit: 10
  });
  const [pagination, setPagination] = useState({});
  const [categories, setCategories] = useState([]);

  useEffect(() => {
    fetchServices();
    fetchCategories();
  }, [filters]);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/electronic-services');
      const data = await response.json();
      if (data.success) {
        // استخراج التصنيفات الفريدة من الخدمات
        const uniqueCategories = [...new Set(data.services.map(service => service.category))];
        setCategories(uniqueCategories.map(cat => ({ id: cat, name: cat })));
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchServices = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      const queryParams = new URLSearchParams(filters).toString();
      
      const response = await fetch(`/api/admin/electronic-services?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      
      if (data.success) {
        setServices(data.services);
        setPagination(data.pagination);
      } else {
        setError(data.message);
      }
    } catch (error) {
      setError('حدث خطأ في جلب البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (!confirm('هل أنت متأكد من حذف هذه الخدمة الإلكترونية؟')) {
      return;
    }

    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/electronic-services?id=${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      
      if (data.success) {
        setServices(services.filter(service => service.id !== id));
        setSuccess('تم حذف الخدمة بنجاح');
        setTimeout(() => setSuccess(''), 3000);
        // إعادة تحميل البيانات للتأكد من التحديث
        setTimeout(() => fetchServices(), 1000);
      } else {
        setError(data.message);
      }
    } catch (error) {
      setError('حدث خطأ في حذف الخدمة');
    }
  };

  const handleStatusToggle = async (id, currentStatus) => {
    try {
      const token = localStorage.getItem('adminToken');
      const newStatus = currentStatus === 'published' ? 'draft' : 'published';

      const response = await fetch(`/api/admin/electronic-services?id=${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          status: newStatus
        })
      });

      const data = await response.json();

      if (data.success) {
        setServices(services.map(service =>
          service.id === id ? { ...service, status: newStatus, updated_at: new Date().toISOString() } : service
        ));
        setSuccess('تم تحديث حالة الخدمة بنجاح');
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(data.message);
      }
    } catch (error) {
      setError('حدث خطأ في تحديث حالة الخدمة');
    }
  };

  const handleFeaturedToggle = async (id, currentFeatured) => {
    try {
      const token = localStorage.getItem('adminToken');
      const newFeatured = !currentFeatured;

      const response = await fetch(`/api/admin/electronic-services?id=${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          featured: newFeatured
        })
      });

      const data = await response.json();

      if (data.success) {
        setServices(services.map(service =>
          service.id === id ? { ...service, featured: newFeatured, updated_at: new Date().toISOString() } : service
        ));
        setSuccess(`تم ${newFeatured ? 'تمييز' : 'إلغاء تمييز'} الخدمة بنجاح`);
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(data.message);
      }
    } catch (error) {
      setError('حدث خطأ في تحديث حالة الخدمة المميزة');
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // إعادة تعيين الصفحة عند تغيير الفلتر
    }));
  };

  if (loading) {
    return (
      <AdminLayout title="إدارة الخدمات الإلكترونية">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-900 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري التحميل...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="إدارة الخدمات الإلكترونية">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة الخدمات الإلكترونية</h1>
            <p className="text-gray-600">إدارة الخدمات الإلكترونية المقدمة للعملاء</p>
          </div>
          <Link href="/admin/electronic-services/new">
            <button className="bg-primary-900 text-white px-4 py-2 rounded-lg hover:bg-primary-800 transition-colors duration-200 flex items-center">
              <FaPlus className="ml-2" />
              إضافة خدمة جديدة
            </button>
          </Link>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            {success}
          </div>
        )}

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* البحث */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                البحث
              </label>
              <div className="relative">
                <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="ابحث في الخدمات..."
                />
              </div>
            </div>

            {/* الحالة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الحالة
              </label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع الحالات</option>
                <option value="published">منشور</option>
                <option value="draft">مسودة</option>
              </select>
            </div>

            {/* التصنيف */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                التصنيف
              </label>
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع التصنيفات</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* عدد النتائج */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                عدد النتائج
              </label>
              <select
                value={filters.limit}
                onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
          </div>
        </div>

        {/* Services List */}
        <div className="bg-white rounded-lg shadow">
          {services.length === 0 ? (
            <div className="p-8 text-center">
              <FaLaptop className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد خدمات إلكترونية</h3>
              <p className="text-gray-600 mb-4">ابدأ بإضافة خدمة إلكترونية جديدة</p>
              <Link href="/admin/electronic-services/new">
                <button className="bg-primary-900 text-white px-4 py-2 rounded-lg hover:bg-primary-800 transition-colors duration-200">
                  إضافة خدمة جديدة
                </button>
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الخدمة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      التصنيف
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      السعر
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المدة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      تاريخ الإضافة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {services.map((service) => (
                    <tr key={service.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          {service.image && (
                            <img
                              src={service.image}
                              alt={service.title}
                              className="h-12 w-16 object-cover rounded-lg ml-4"
                            />
                          )}
                          <div>
                            <div className="text-sm font-medium text-gray-900 flex items-center">
                              {service.title}
                              {service.featured && (
                                <FaStar className="text-yellow-500 mr-2" />
                              )}
                            </div>
                            <div className="text-sm text-gray-500">
                              {service.excerpt}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {service.category}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {service.price} ريال
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {service.duration}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={() => handleStatusToggle(service.id, service.status)}
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            service.status === 'published'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          {service.status === 'published' ? (
                            <>
                              <FaEye className="ml-1" />
                              منشور
                            </>
                          ) : (
                            <>
                              <FaEyeSlash className="ml-1" />
                              مسودة
                            </>
                          )}
                        </button>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <DateDisplay dateString={service.created_at} />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={() => handleFeaturedToggle(service.id, service.featured)}
                            className={`${
                              service.featured ? 'text-yellow-600' : 'text-gray-400'
                            } hover:text-yellow-500`}
                            title={service.featured ? 'إلغاء التمييز' : 'جعل مميز'}
                          >
                            {service.featured ? <FaStar /> : <FaRegStar />}
                          </button>
                          <Link href={`/admin/electronic-services/edit/${service.id}`}>
                            <button className="text-primary-600 hover:text-primary-900">
                              <FaEdit />
                            </button>
                          </Link>
                          <button
                            onClick={() => handleDelete(service.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <FaTrash />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  عرض {((pagination.page - 1) * pagination.limit) + 1} إلى {Math.min(pagination.page * pagination.limit, pagination.total)} من {pagination.total} نتيجة
                </div>
                <div className="flex space-x-2 space-x-reverse">
                  {Array.from({ length: pagination.pages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => handleFilterChange('page', page)}
                      className={`px-3 py-1 rounded ${
                        page === pagination.page
                          ? 'bg-primary-900 text-white'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}
