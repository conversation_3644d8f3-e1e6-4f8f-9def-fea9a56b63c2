import { useState, useEffect } from 'react';
import AdminLayout from '../../../components/admin/AdminLayout';
import {
  FaEnvelope,
  FaEnvelopeOpen,
  FaReply,
  FaTrash,
  <PERSON>a<PERSON><PERSON>ch,
  <PERSON>a<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON>ser,
  FaPhone
} from 'react-icons/fa';

export default function MessagesManagement() {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState(null);

  useEffect(() => {
    fetchMessages();
  }, []);

  const fetchMessages = async () => {
    try {
      // بيانات وهمية للرسائل
      const mockData = [
        {
          id: 1,
          name: 'أحمد محمد علي',
          email: '<EMAIL>',
          phone: '+20 2 1234 5678',
          company: 'شركة التطوير المصرية',
          subject: 'استفسار عن خدمات قانون الشركات',
          message: 'أحتاج إلى استشارة قانونية حول تأسيس شركة جديدة في مصر. ما هي الإجراءات المطلوبة والوثائق اللازمة؟',
          serviceType: 'قانون الشركات والتجارة',
          status: 'unread',
          priority: 'high',
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-15T10:30:00Z'
        },
        {
          id: 2,
          name: 'فاطمة أحمد حسن',
          email: '<EMAIL>',
          phone: '+20 2 1234 5679',
          company: '',
          subject: 'استشارة في قضية عقارية',
          message: 'لدي مشكلة في عقد شراء شقة، وأحتاج إلى مراجعة قانونية للعقد قبل التوقيع النهائي.',
          serviceType: 'القانون العقاري',
          status: 'read',
          priority: 'medium',
          createdAt: '2024-01-14T14:20:00Z',
          updatedAt: '2024-01-14T15:45:00Z'
        },
        {
          id: 3,
          name: 'محمد عبد الرحمن',
          email: '<EMAIL>',
          phone: '+20 2 1234 5680',
          company: 'مؤسسة النور للتجارة',
          subject: 'نزاع تجاري',
          message: 'نواجه نزاع تجاري مع أحد الموردين، ونحتاج إلى تمثيل قانوني لحل هذه المسألة.',
          serviceType: 'التقاضي وحل النزاعات',
          status: 'replied',
          priority: 'high',
          createdAt: '2024-01-13T09:15:00Z',
          updatedAt: '2024-01-13T16:30:00Z'
        },
        {
          id: 4,
          name: 'سارة محمود',
          email: '<EMAIL>',
          phone: '+20 2 1234 5681',
          company: '',
          subject: 'استفسار عن قانون العمل',
          message: 'أحتاج إلى معرفة حقوقي كموظفة في حالة الفصل من العمل، وما هي الإجراءات القانونية المتاحة.',
          serviceType: 'قانون العمل والتوظيف',
          status: 'unread',
          priority: 'low',
          createdAt: '2024-01-12T11:45:00Z',
          updatedAt: '2024-01-12T11:45:00Z'
        }
      ];
      
      setMessages(mockData);
      setLoading(false);
    } catch (error) {
      console.error('خطأ في جلب الرسائل:', error);
      setLoading(false);
    }
  };

  const handleMarkAsRead = async (id) => {
    try {
      setMessages(prev => prev.map(msg => 
        msg.id === id ? { ...msg, status: 'read' } : msg
      ));
    } catch (error) {
      console.error('خطأ في تحديث حالة الرسالة:', error);
    }
  };

  const handleDelete = async (id) => {
    try {
      setMessages(prev => prev.filter(msg => msg.id !== id));
      setDeleteConfirm(null);
    } catch (error) {
      console.error('خطأ في حذف الرسالة:', error);
    }
  };

  const filteredMessages = messages.filter(message => {
    const matchesSearch = message.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.subject.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterStatus === 'all' || message.status === filterStatus;
    
    return matchesSearch && matchesFilter;
  });

  const getStatusBadge = (status) => {
    const badges = {
      unread: { color: 'bg-red-100 text-red-800', text: 'غير مقروءة' },
      read: { color: 'bg-blue-100 text-blue-800', text: 'مقروءة' },
      replied: { color: 'bg-green-100 text-green-800', text: 'تم الرد' }
    };
    return badges[status] || badges.unread;
  };

  const getPriorityBadge = (priority) => {
    const badges = {
      high: { color: 'bg-red-100 text-red-800', text: 'عالية' },
      medium: { color: 'bg-yellow-100 text-yellow-800', text: 'متوسطة' },
      low: { color: 'bg-gray-100 text-gray-800', text: 'منخفضة' }
    };
    return badges[priority] || badges.medium;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <AdminLayout title="إدارة الرسائل">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-900 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري التحميل...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="إدارة الرسائل">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة الرسائل</h1>
            <p className="text-gray-600">رسائل العملاء والاستفسارات</p>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              إجمالي الرسائل: {messages.length}
            </span>
            <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">
              غير مقروءة: {messages.filter(m => m.status === 'unread').length}
            </span>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="البحث في الرسائل..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="all">جميع الرسائل</option>
                <option value="unread">غير مقروءة</option>
                <option value="read">مقروءة</option>
                <option value="replied">تم الرد عليها</option>
              </select>
            </div>
          </div>
        </div>

        {/* Messages List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {filteredMessages.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {filteredMessages.map((message) => (
                <div
                  key={message.id}
                  className={`p-6 hover:bg-gray-50 transition-colors duration-200 ${
                    message.status === 'unread' ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 truncate">
                          {message.name}
                        </h3>
                        <div className="flex gap-2">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(message.status).color}`}>
                            {getStatusBadge(message.status).text}
                          </span>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityBadge(message.priority).color}`}>
                            {getPriorityBadge(message.priority).text}
                          </span>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                        <div className="flex items-center text-sm text-gray-600">
                          <FaEnvelope className="ml-2 text-gray-400" />
                          <span>{message.email}</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <FaPhone className="ml-2 text-gray-400" />
                          <span>{message.phone}</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <FaClock className="ml-2 text-gray-400" />
                          <span>{formatDate(message.createdAt)}</span>
                        </div>
                      </div>

                      <h4 className="font-medium text-gray-900 mb-2">{message.subject}</h4>
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">{message.message}</p>
                      
                      {message.serviceType && (
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                          {message.serviceType}
                        </span>
                      )}
                    </div>

                    <div className="flex items-center gap-2 mr-4">
                      <button
                        onClick={() => setSelectedMessage(message)}
                        className="bg-primary-100 text-primary-700 p-2 rounded-lg hover:bg-primary-200 transition-colors duration-200"
                        title="عرض التفاصيل"
                      >
                        <FaEye />
                      </button>
                      {message.status === 'unread' && (
                        <button
                          onClick={() => handleMarkAsRead(message.id)}
                          className="bg-blue-100 text-blue-700 p-2 rounded-lg hover:bg-blue-200 transition-colors duration-200"
                          title="تحديد كمقروءة"
                        >
                          <FaEnvelopeOpen />
                        </button>
                      )}
                      <button
                        className="bg-green-100 text-green-700 p-2 rounded-lg hover:bg-green-200 transition-colors duration-200"
                        title="رد"
                      >
                        <FaReply />
                      </button>
                      <button
                        onClick={() => setDeleteConfirm(message.id)}
                        className="bg-red-100 text-red-700 p-2 rounded-lg hover:bg-red-200 transition-colors duration-200"
                        title="حذف"
                      >
                        <FaTrash />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <FaEnvelope className="text-6xl text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد رسائل</h3>
              <p className="text-gray-600">لم يتم العثور على رسائل تطابق البحث</p>
            </div>
          )}
        </div>

        {/* Message Details Modal */}
        {selectedMessage && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">تفاصيل الرسالة</h3>
                  <button
                    onClick={() => setSelectedMessage(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                </div>
              </div>
              
              <div className="p-6 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">الاسم</label>
                    <p className="text-gray-900">{selectedMessage.name}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">البريد الإلكتروني</label>
                    <p className="text-gray-900">{selectedMessage.email}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">الهاتف</label>
                    <p className="text-gray-900">{selectedMessage.phone}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">الشركة</label>
                    <p className="text-gray-900">{selectedMessage.company || 'غير محدد'}</p>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">الموضوع</label>
                  <p className="text-gray-900">{selectedMessage.subject}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">نوع الخدمة</label>
                  <p className="text-gray-900">{selectedMessage.serviceType}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">الرسالة</label>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-gray-900 whitespace-pre-wrap">{selectedMessage.message}</p>
                  </div>
                </div>
                
                <div className="flex gap-3 pt-4">
                  <button className="flex-1 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200">
                    رد على الرسالة
                  </button>
                  <button
                    onClick={() => handleMarkAsRead(selectedMessage.id)}
                    className="bg-blue-100 text-blue-700 px-4 py-2 rounded-lg hover:bg-blue-200 transition-colors duration-200"
                  >
                    تحديد كمقروءة
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {deleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">تأكيد الحذف</h3>
              <p className="text-gray-600 mb-6">هل أنت متأكد من حذف هذه الرسالة؟ لا يمكن التراجع عن هذا الإجراء.</p>
              <div className="flex gap-3">
                <button
                  onClick={() => handleDelete(deleteConfirm)}
                  className="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors duration-200"
                >
                  حذف
                </button>
                <button
                  onClick={() => setDeleteConfirm(null)}
                  className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors duration-200"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
