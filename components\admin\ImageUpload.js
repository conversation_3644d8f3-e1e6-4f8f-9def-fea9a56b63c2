import { useState, useRef } from 'react';
import { FaUpload, FaImage, FaTimes, FaSpinner } from 'react-icons/fa';

const ImageUpload = ({ 
  value, 
  onChange, 
  label = "رفع صورة", 
  placeholder = "اضغط لرفع صورة أو اسحب الصورة هنا",
  accept = "image/*",
  maxSize = 5 * 1024 * 1024, // 5MB
  className = "",
  required = false 
}) => {
  const [uploading, setUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const [error, setError] = useState('');
  const fileInputRef = useRef(null);

  const handleFileSelect = async (file) => {
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
      setError('يرجى اختيار ملف صورة صالح');
      return;
    }

    // التحقق من حجم الملف
    if (file.size > maxSize) {
      setError(`حجم الملف كبير جداً. الحد الأقصى ${(maxSize / 1024 / 1024).toFixed(1)} MB`);
      return;
    }

    setError('');
    setUploading(true);

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (data.success) {
        onChange(data.image.url);
      } else {
        setError(data.error || 'حدث خطأ في رفع الصورة');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      setError('حدث خطأ في رفع الصورة');
    } finally {
      setUploading(false);
    }
  };

  const handleFileInputChange = (e) => {
    const file = e.target.files[0];
    handleFileSelect(file);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleUrlChange = (e) => {
    onChange(e.target.value);
    setError('');
  };

  const clearImage = () => {
    onChange('');
    setError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <label className="block text-gray-700 font-semibold mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>

      {/* منطقة رفع الصور */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 cursor-pointer ${
          dragOver
            ? 'border-primary-500 bg-primary-50'
            : uploading
            ? 'border-gray-300 bg-gray-50'
            : 'border-gray-300 hover:border-primary-400 hover:bg-gray-50'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => !uploading && fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileInputChange}
          className="hidden"
          disabled={uploading}
        />

        {uploading ? (
          <div className="flex flex-col items-center">
            <FaSpinner className="text-3xl text-primary-600 mb-2 animate-spin" />
            <span className="text-primary-600 font-medium">جاري رفع الصورة...</span>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <FaUpload className="text-3xl text-gray-400 mb-2" />
            <span className="text-gray-600 font-medium">{placeholder}</span>
            <span className="text-sm text-gray-500 mt-1">
              JPG, PNG, WebP, GIF (حد أقصى {(maxSize / 1024 / 1024).toFixed(1)}MB)
            </span>
          </div>
        )}
      </div>

      {/* أو إدخال رابط */}
      <div className="text-center text-gray-500 text-sm">أو</div>
      
      <div>
        <label className="block text-gray-700 font-medium mb-2">
          رابط الصورة
        </label>
        <input
          type="url"
          value={value}
          onChange={handleUrlChange}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          placeholder="https://example.com/image.jpg"
          disabled={uploading}
        />
      </div>

      {/* رسالة الخطأ */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
          {error}
        </div>
      )}

      {/* معاينة الصورة */}
      {value && !uploading && (
        <div>
          <label className="block text-gray-700 font-medium mb-2">معاينة الصورة</label>
          <div className="relative inline-block">
            <img 
              src={value} 
              alt="معاينة الصورة" 
              className="w-48 h-32 object-cover rounded-lg border shadow-sm"
              onError={(e) => {
                e.target.style.display = 'none';
                setError('لا يمكن تحميل الصورة من الرابط المحدد');
              }}
              onLoad={() => setError('')}
            />
            <button
              type="button"
              onClick={clearImage}
              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600 transition-colors"
              title="حذف الصورة"
            >
              <FaTimes />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUpload;
