// تحسينات الأمان لـ Next.js 15

import crypto from 'crypto';
import rateLimit from 'express-rate-limit';

// إعدادات الأمان العامة
export const securityConfig = {
  // إعدادات التشفير
  encryption: {
    algorithm: 'aes-256-gcm',
    keyLength: 32,
    ivLength: 16,
    tagLength: 16,
  },
  
  // إعدادات كلمات المرور
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 دقيقة
  },
  
  // إعدادات الجلسات
  session: {
    maxAge: 24 * 60 * 60 * 1000, // 24 ساعة
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    sameSite: 'strict',
  },
  
  // إعدادات CSRF
  csrf: {
    enabled: true,
    secret: process.env.CSRF_SECRET || 'default-csrf-secret',
  },
};

// دالة لتشفير البيانات
export function encrypt(text, key) {
  try {
    const iv = crypto.randomBytes(securityConfig.encryption.ivLength);
    const cipher = crypto.createCipher(securityConfig.encryption.algorithm, key);
    cipher.setAAD(Buffer.from('additional-data'));
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex'),
    };
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('فشل في تشفير البيانات');
  }
}

// دالة لفك تشفير البيانات
export function decrypt(encryptedData, key) {
  try {
    const { encrypted, iv, tag } = encryptedData;
    const decipher = crypto.createDecipher(securityConfig.encryption.algorithm, key);
    
    decipher.setAAD(Buffer.from('additional-data'));
    decipher.setAuthTag(Buffer.from(tag, 'hex'));
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('فشل في فك تشفير البيانات');
  }
}

// دالة لتوليد hash آمن
export function generateSecureHash(data, salt) {
  const hash = crypto.createHash('sha256');
  hash.update(data + salt);
  return hash.digest('hex');
}

// دالة للتحقق من قوة كلمة المرور
export function validatePasswordStrength(password) {
  const errors = [];
  
  if (password.length < securityConfig.password.minLength) {
    errors.push(`كلمة المرور يجب أن تكون ${securityConfig.password.minLength} أحرف على الأقل`);
  }
  
  if (securityConfig.password.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
  }
  
  if (securityConfig.password.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل');
  }
  
  if (securityConfig.password.requireNumbers && !/\d/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
  }
  
  if (securityConfig.password.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    strength: calculatePasswordStrength(password),
  };
}

// دالة لحساب قوة كلمة المرور
function calculatePasswordStrength(password) {
  let score = 0;
  
  // الطول
  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;
  
  // التنوع
  if (/[a-z]/.test(password)) score += 1;
  if (/[A-Z]/.test(password)) score += 1;
  if (/\d/.test(password)) score += 1;
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;
  
  // التعقيد
  if (password.length >= 16) score += 1;
  if (/[^\w\s]/.test(password)) score += 1;
  
  if (score <= 2) return 'ضعيف';
  if (score <= 4) return 'متوسط';
  if (score <= 6) return 'قوي';
  return 'قوي جداً';
}

// دالة لتنظيف المدخلات من XSS
export function sanitizeInput(input) {
  if (typeof input !== 'string') return input;
  
  return input
    .replace(/[<>]/g, '') // إزالة علامات HTML
    .replace(/javascript:/gi, '') // إزالة JavaScript URLs
    .replace(/on\w+=/gi, '') // إزالة event handlers
    .trim();
}

// دالة للتحقق من صحة البريد الإلكتروني
export function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// دالة للتحقق من صحة رقم الهاتف السعودي
export function validateSaudiPhone(phone) {
  const phoneRegex = /^(\+966|966|0)?[5][0-9]{8}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}

// دالة لتحديد معدل الطلبات
export const createRateLimit = (options = {}) => {
  return rateLimit({
    windowMs: options.windowMs || 15 * 60 * 1000, // 15 دقيقة
    max: options.max || 100, // حد أقصى 100 طلب
    message: options.message || 'تم تجاوز الحد المسموح من الطلبات',
    standardHeaders: true,
    legacyHeaders: false,
    ...options,
  });
};

// دالة للتحقق من CSRF Token
export function validateCSRFToken(token, session) {
  if (!securityConfig.csrf.enabled) return true;
  
  const expectedToken = generateCSRFToken(session);
  return crypto.timingSafeEqual(
    Buffer.from(token),
    Buffer.from(expectedToken)
  );
}

// دالة لتوليد CSRF Token
export function generateCSRFToken(session) {
  const data = session.id + securityConfig.csrf.secret;
  return crypto.createHash('sha256').update(data).digest('hex');
}

// دالة للتحقق من صحة الملفات المرفوعة
export function validateFileUpload(file, options = {}) {
  const errors = [];
  
  // التحقق من نوع الملف
  if (options.allowedTypes && !options.allowedTypes.includes(file.mimetype)) {
    errors.push('نوع الملف غير مدعوم');
  }
  
  // التحقق من حجم الملف
  if (options.maxSize && file.size > options.maxSize) {
    errors.push(`حجم الملف يجب أن يكون أقل من ${options.maxSize / 1024 / 1024}MB`);
  }
  
  // التحقق من امتداد الملف
  if (options.allowedExtensions) {
    const extension = file.originalname.split('.').pop().toLowerCase();
    if (!options.allowedExtensions.includes(extension)) {
      errors.push('امتداد الملف غير مدعوم');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

// دالة لتسجيل محاولات الأمان
export function logSecurityEvent(event, details = {}) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event,
    ip: details.ip,
    userAgent: details.userAgent,
    userId: details.userId,
    details: details.extra,
  };
  
  // في بيئة الإنتاج، يجب حفظ هذا في قاعدة بيانات أو ملف log
  if (process.env.NODE_ENV === 'development') {
    console.log('🔒 Security Event:', logEntry);
  }
  
  // إرسال تنبيه في حالة الأحداث الحرجة
  if (['failed_login', 'brute_force', 'suspicious_activity'].includes(event)) {
    // إرسال تنبيه للمدير
    sendSecurityAlert(logEntry);
  }
}

// دالة لإرسال تنبيهات الأمان
function sendSecurityAlert(logEntry) {
  // هنا يمكن إرسال بريد إلكتروني أو إشعار
  console.warn('🚨 Security Alert:', logEntry);
}

// تصدير جميع الدوال
export default {
  securityConfig,
  encrypt,
  decrypt,
  generateSecureHash,
  validatePasswordStrength,
  sanitizeInput,
  validateEmail,
  validateSaudiPhone,
  createRateLimit,
  validateCSRFToken,
  generateCSRFToken,
  validateFileUpload,
  logSecurityEvent,
};
