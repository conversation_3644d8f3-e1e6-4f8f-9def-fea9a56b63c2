import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaEyeDropper } from 'react-icons/fa';

const ColorPicker = ({ 
  selectedColor, 
  onColorChange, 
  label = 'اختر اللون',
  type = 'text', // 'text' or 'background'
  className = '' 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [customColor, setCustomColor] = useState(selectedColor);

  // مجموعة ألوان محددة مسبقاً
  const colorPalettes = {
    basic: {
      name: 'الألوان الأساسية',
      colors: [
        { name: 'أسود', value: '#000000' },
        { name: 'أبيض', value: '#FFFFFF' },
        { name: 'رمادي', value: '#808080' },
        { name: 'أحمر', value: '#FF0000' },
        { name: 'أخضر', value: '#00FF00' },
        { name: 'أزرق', value: '#0000FF' },
        { name: 'أصف<PERSON>', value: '#FFFF00' },
        { name: 'برتقالي', value: '#FFA500' }
      ]
    },
    professional: {
      name: 'ألوان احترافية',
      colors: [
        { name: 'أزرق داكن', value: '#1e3a8a' },
        { name: 'أزرق متوسط', value: '#3b82f6' },
        { name: 'أزرق فاتح', value: '#60a5fa' },
        { name: 'أخضر داكن', value: '#166534' },
        { name: 'أخضر متوسط', value: '#22c55e' },
        { name: 'أخضر فاتح', value: '#4ade80' },
        { name: 'أحمر داكن', value: '#991b1b' },
        { name: 'أحمر متوسط', value: '#ef4444' }
      ]
    },
    warm: {
      name: 'ألوان دافئة',
      colors: [
        { name: 'برتقالي محروق', value: '#ea580c' },
        { name: 'أصفر ذهبي', value: '#f59e0b' },
        { name: 'أحمر وردي', value: '#f43f5e' },
        { name: 'بنفسجي', value: '#a855f7' },
        { name: 'وردي', value: '#ec4899' },
        { name: 'برونزي', value: '#92400e' },
        { name: 'بني', value: '#78350f' },
        { name: 'كريمي', value: '#fef3c7' }
      ]
    },
    cool: {
      name: 'ألوان باردة',
      colors: [
        { name: 'تركوازي', value: '#06b6d4' },
        { name: 'أزرق سماوي', value: '#0ea5e9' },
        { name: 'بنفسجي فاتح', value: '#8b5cf6' },
        { name: 'أخضر نعناعي', value: '#10b981' },
        { name: 'أزرق بحري', value: '#1e40af' },
        { name: 'رمادي أزرق', value: '#475569' },
        { name: 'أخضر زيتوني', value: '#65a30d' },
        { name: 'أزرق رمادي', value: '#334155' }
      ]
    }
  };

  const handleColorSelect = (color) => {
    onColorChange(color);
    setCustomColor(color);
    setIsOpen(false);
  };

  const handleCustomColorChange = (e) => {
    const color = e.target.value;
    setCustomColor(color);
    onColorChange(color);
  };

  return (
    <div className={`relative ${className}`}>
      {/* زر اختيار اللون */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-3 px-4 py-3 bg-white border border-gray-300 rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-200"
      >
        <div className="flex items-center gap-2">
          {type === 'text' ? <FaPalette className="text-gray-500" /> : <FaEyeDropper className="text-gray-500" />}
          <span className="text-sm font-medium text-gray-700">{label}</span>
        </div>
        
        <div className="flex items-center gap-2">
          <div 
            className="w-6 h-6 rounded border-2 border-gray-300"
            style={{ backgroundColor: selectedColor }}
          />
          <span className="text-xs text-gray-500 font-mono">{selectedColor}</span>
        </div>
      </button>

      {/* لوحة الألوان */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-50 p-4 min-w-80">
          <div className="space-y-4">
            {/* اختيار لون مخصص */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                لون مخصص
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  value={customColor}
                  onChange={handleCustomColorChange}
                  className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                />
                <input
                  type="text"
                  value={customColor}
                  onChange={(e) => handleCustomColorChange(e)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded text-sm font-mono"
                  placeholder="#000000"
                />
              </div>
            </div>

            {/* مجموعات الألوان */}
            {Object.entries(colorPalettes).map(([key, palette]) => (
              <div key={key}>
                <h4 className="text-sm font-medium text-gray-700 mb-2">{palette.name}</h4>
                <div className="grid grid-cols-4 gap-2">
                  {palette.colors.map((color) => (
                    <button
                      key={color.value}
                      type="button"
                      onClick={() => handleColorSelect(color.value)}
                      className={`relative w-full h-10 rounded border-2 transition-all duration-200 hover:scale-105 ${
                        selectedColor === color.value 
                          ? 'border-primary-500 ring-2 ring-primary-200' 
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                      style={{ backgroundColor: color.value }}
                      title={color.name}
                    >
                      {selectedColor === color.value && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <FaCheck 
                            className={`text-sm ${
                              color.value === '#FFFFFF' || color.value === '#FFFF00' || color.value === '#fef3c7'
                                ? 'text-gray-800' 
                                : 'text-white'
                            }`} 
                          />
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              </div>
            ))}

            {/* أزرار التحكم */}
            <div className="flex justify-between pt-2 border-t border-gray-200">
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors duration-200"
              >
                إلغاء
              </button>
              
              <button
                type="button"
                onClick={() => {
                  onColorChange(customColor);
                  setIsOpen(false);
                }}
                className="px-4 py-2 bg-primary-600 text-white text-sm rounded hover:bg-primary-700 transition-colors duration-200"
              >
                تطبيق
              </button>
            </div>
          </div>
        </div>
      )}

      {/* خلفية شفافة لإغلاق اللوحة */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default ColorPicker;
