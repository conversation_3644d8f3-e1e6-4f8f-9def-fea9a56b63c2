import db from '../../../../lib/database.js';
import { requireAdmin, errorHandler } from '../../../../lib/middleware.js';

const handler = async (req, res) => {
  const { id } = req.query;

  switch (req.method) {
    case 'GET':
      return getArticle(req, res, id);
    case 'PUT':
      return updateArticle(req, res, id);
    case 'DELETE':
      return deleteArticle(req, res, id);
    default:
      return res.status(405).json({ 
        success: false, 
        message: 'Method not allowed' 
      });
  }
};

// جلب مقال واحد
const getArticle = async (req, res, id) => {
  try {
    // قراءة البيانات من ملف JSON
    const fs = require('fs');
    const path = require('path');
    const articlesPath = path.join(process.cwd(), 'data', 'articles.json');

    let articles = [];
    if (fs.existsSync(articlesPath)) {
      const data = fs.readFileSync(articlesPath, 'utf8');
      articles = JSON.parse(data);
    }

    // البحث عن المقال
    const article = articles.find(article => article.id === id || article.id === parseInt(id));

    if (!article) {
      return res.status(404).json({
        success: false,
        message: 'المقال غير موجود'
      });
    }

    return res.status(200).json({
      success: true,
      article
    });
  } catch (error) {
    console.error('Error fetching article:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب المقال'
    });
  }
};

// تحديث مقال
const updateArticle = async (req, res, id) => {
  try {
    const {
      title,
      slug,
      excerpt,
      content,
      image,
      author,
      category,
      tags,
      status,
      featured
    } = req.body;

    if (!title || !content) {
      return res.status(400).json({
        success: false,
        message: 'العنوان والمحتوى مطلوبان'
      });
    }

    // إنشاء slug تلقائياً إذا لم يتم توفيره
    const finalSlug = slug || title.toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-');

    // قراءة البيانات الحالية
    const fs = require('fs');
    const path = require('path');
    const articlesPath = path.join(process.cwd(), 'data', 'articles.json');

    let articles = [];
    if (fs.existsSync(articlesPath)) {
      const data = fs.readFileSync(articlesPath, 'utf8');
      articles = JSON.parse(data);
    }

    // البحث عن المقال
    const articleIndex = articles.findIndex(article => article.id === id || article.id === parseInt(id));

    if (articleIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'المقال غير موجود'
      });
    }

    // التحقق من عدم تكرار الـ slug
    const duplicateSlug = articles.find(article =>
      article.slug === finalSlug && article.id !== id && article.id !== parseInt(id)
    );

    if (duplicateSlug) {
      return res.status(400).json({
        success: false,
        message: 'الرابط المختصر مستخدم بالفعل'
      });
    }

    // تحديث المقال
    articles[articleIndex] = {
      ...articles[articleIndex],
      title,
      slug: finalSlug,
      excerpt,
      content,
      image,
      author,
      category,
      tags: tags ? (typeof tags === 'string' ? tags.split(',').map(tag => tag.trim()).filter(tag => tag) : tags) : [],
      status,
      featured: !!featured,
      updated_at: new Date().toISOString()
    };

    // حفظ البيانات
    fs.writeFileSync(articlesPath, JSON.stringify(articles, null, 2));

    return res.status(200).json({
      success: true,
      message: 'تم تحديث المقال بنجاح',
      article: articles[articleIndex]
    });
  } catch (error) {
    console.error('Error updating article:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في تحديث المقال'
    });
  }
};

// حذف مقال
const deleteArticle = async (req, res, id) => {
  try {
    // قراءة البيانات الحالية
    const fs = require('fs');
    const path = require('path');
    const articlesPath = path.join(process.cwd(), 'data', 'articles.json');

    let articles = [];
    if (fs.existsSync(articlesPath)) {
      const data = fs.readFileSync(articlesPath, 'utf8');
      articles = JSON.parse(data);
    }

    // البحث عن المقال
    const articleIndex = articles.findIndex(article => article.id === id || article.id === parseInt(id));

    if (articleIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'المقال غير موجود'
      });
    }

    // حذف المقال
    articles.splice(articleIndex, 1);

    // حفظ البيانات
    fs.writeFileSync(articlesPath, JSON.stringify(articles, null, 2));

    return res.status(200).json({
      success: true,
      message: 'تم حذف المقال بنجاح'
    });
  } catch (error) {
    console.error('Error deleting article:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في حذف المقال'
    });
  }
};

export default requireAdmin(errorHandler(handler));
