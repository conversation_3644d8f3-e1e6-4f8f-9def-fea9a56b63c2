{"name": "law-firm-website", "version": "1.0.0", "description": "موقع مكتب محاماة واستشارات قانونية", "type": "module", "private": true, "scripts": {"dev": "next dev", "dev:clean": "npm run clean && npm run dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rimraf .next && rimraf node_modules/.cache", "clean:all": "rimraf .next && rimraf node_modules/.cache && rimraf out", "migrate": "node scripts/migrate-to-mongodb.js", "verify": "node scripts/verify-migration.js", "check-paths": "node scripts/check-paths.js", "test-login": "node scripts/test-login-system.js"}, "dependencies": {"@babel/plugin-transform-runtime": "^7.27.4", "@babel/runtime": "^7.27.4", "bcryptjs": "^2.4.3", "critters": "^0.0.23", "formidable": "^3.5.4", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "next": "^15.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0"}, "devDependencies": {"@types/node": "22.15.29", "@types/react": "19.1.6", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "^15.0.3", "postcss": "^8.4.47", "rimraf": "^5.0.5", "tailwindcss": "^3.4.13", "typescript": "^5.6.3"}}