import mongoose from 'mongoose';

const expertiseSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'عنوان مجال الخبرة مطلوب'],
    trim: true,
    maxlength: [200, 'العنوان يجب أن يكون أقل من 200 حرف']
  },
  slug: {
    type: String,
    required: [true, 'الرابط المختصر مطلوب'],
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^[a-z0-9-]+$/, 'الرابط المختصر يجب أن يحتوي على أحرف إنجليزية وأرقام وشرطات فقط']
  },
  description: {
    type: String,
    required: [true, 'وصف مجال الخبرة مطلوب'],
    trim: true,
    maxlength: [1000, 'الوصف يجب أن يكون أقل من 1000 حرف']
  },
  content: {
    type: String,
    trim: true
  },
  icon: {
    type: String,
    trim: true
  },
  image: {
    type: String,
    trim: true
  },
  color: {
    type: String,
    trim: true,
    match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'لون غير صحيح'],
    default: '#3B82F6'
  },
  category: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'archived'],
    default: 'active'
  },
  featured: {
    type: Boolean,
    default: false
  },
  order: {
    type: Number,
    default: 0
  },
  services: [{
    title: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    isActive: {
      type: Boolean,
      default: true
    }
  }],
  keyPoints: [{
    type: String,
    trim: true
  }],
  relatedLaws: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    url: {
      type: String,
      trim: true
    }
  }],
  caseStudies: [{
    title: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    outcome: {
      type: String,
      trim: true
    },
    year: {
      type: Number,
      min: 1990,
      max: new Date().getFullYear()
    },
    isPublic: {
      type: Boolean,
      default: false
    }
  }],
  teamMembers: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TeamMember'
  }],
  statistics: {
    casesHandled: {
      type: Number,
      default: 0
    },
    successRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    yearsOfExperience: {
      type: Number,
      default: 0
    },
    clientSatisfaction: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    }
  },
  pricing: {
    consultationFee: {
      type: Number,
      min: 0
    },
    hourlyRate: {
      type: Number,
      min: 0
    },
    currency: {
      type: String,
      default: 'SAR'
    },
    notes: {
      type: String,
      trim: true
    }
  },
  contactInfo: {
    email: {
      type: String,
      trim: true,
      lowercase: true
    },
    phone: {
      type: String,
      trim: true
    },
    extension: {
      type: String,
      trim: true
    }
  },
  meta: {
    title: {
      type: String,
      trim: true,
      maxlength: [60, 'عنوان SEO يجب أن يكون أقل من 60 حرف']
    },
    description: {
      type: String,
      trim: true,
      maxlength: [160, 'وصف SEO يجب أن يكون أقل من 160 حرف']
    },
    keywords: {
      type: String,
      trim: true
    }
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
});

// فهرسة للبحث والأداء
expertiseSchema.index({ slug: 1 }, { unique: true });
expertiseSchema.index({ status: 1 });
expertiseSchema.index({ category: 1 });
expertiseSchema.index({ featured: 1 });
expertiseSchema.index({ order: 1 });
expertiseSchema.index({ 
  title: 'text', 
  description: 'text', 
  content: 'text',
  keyPoints: 'text'
}, {
  weights: {
    title: 10,
    description: 5,
    keyPoints: 3,
    content: 1
  }
});

// Virtual لحساب عدد الخدمات النشطة
expertiseSchema.virtual('activeServicesCount').get(function() {
  return this.services.filter(service => service.isActive).length;
});

// Virtual لحساب عدد دراسات الحالة العامة
expertiseSchema.virtual('publicCaseStudiesCount').get(function() {
  return this.caseStudies.filter(study => study.isPublic).length;
});

// طرق النموذج
expertiseSchema.methods.addService = function(service) {
  this.services.push(service);
  return this.save();
};

expertiseSchema.methods.removeService = function(serviceId) {
  this.services = this.services.filter(service => 
    service._id.toString() !== serviceId.toString()
  );
  return this.save();
};

expertiseSchema.methods.addKeyPoint = function(point) {
  if (!this.keyPoints.includes(point)) {
    this.keyPoints.push(point);
    return this.save();
  }
  return this;
};

expertiseSchema.methods.removeKeyPoint = function(point) {
  this.keyPoints = this.keyPoints.filter(p => p !== point);
  return this.save();
};

expertiseSchema.methods.addCaseStudy = function(caseStudy) {
  this.caseStudies.push(caseStudy);
  return this.save();
};

expertiseSchema.methods.addTeamMember = function(memberId) {
  if (!this.teamMembers.includes(memberId)) {
    this.teamMembers.push(memberId);
    return this.save();
  }
  return this;
};

expertiseSchema.methods.removeTeamMember = function(memberId) {
  this.teamMembers = this.teamMembers.filter(id => 
    id.toString() !== memberId.toString()
  );
  return this.save();
};

expertiseSchema.methods.updateStatistics = function(stats) {
  Object.assign(this.statistics, stats);
  return this.save();
};

// طرق ثابتة
expertiseSchema.statics.findActive = function() {
  return this.find({ status: 'active' }).sort({ order: 1, title: 1 });
};

expertiseSchema.statics.findFeatured = function() {
  return this.find({ status: 'active', featured: true }).sort({ order: 1, title: 1 });
};

expertiseSchema.statics.findByCategory = function(category) {
  return this.find({ status: 'active', category }).sort({ order: 1, title: 1 });
};

expertiseSchema.statics.searchExpertise = function(query) {
  return this.find(
    { 
      $text: { $search: query },
      status: 'active'
    },
    { score: { $meta: 'textScore' } }
  ).sort({ score: { $meta: 'textScore' } });
};

expertiseSchema.statics.reorderExpertise = async function(expertiseIds) {
  const updates = expertiseIds.map((id, index) => ({
    updateOne: {
      filter: { _id: id },
      update: { order: index }
    }
  }));
  
  return this.bulkWrite(updates);
};

expertiseSchema.statics.getStats = async function() {
  const pipeline = [
    {
      $group: {
        _id: null,
        totalExpertise: { $sum: 1 },
        activeExpertise: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        totalCases: { $sum: '$statistics.casesHandled' },
        averageSuccessRate: { $avg: '$statistics.successRate' }
      }
    }
  ];
  
  const result = await this.aggregate(pipeline);
  return result[0] || {
    totalExpertise: 0,
    activeExpertise: 0,
    totalCases: 0,
    averageSuccessRate: 0
  };
};

// تصدير النموذج
const Expertise = mongoose.models.Expertise || mongoose.model('Expertise', expertiseSchema);

export default Expertise;
