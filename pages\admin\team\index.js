import { useState, useEffect } from 'react';
import AdminLayout from '../../../components/admin/AdminLayout';
import Link from 'next/link';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaUser,
  FaEnvelope,
  FaPhone,
  FaSearch
} from 'react-icons/fa';

export default function TeamManagement() {
  const [teamMembers, setTeamMembers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [deleteConfirm, setDeleteConfirm] = useState(null);

  useEffect(() => {
    fetchTeamMembers();
  }, []);

  const fetchTeamMembers = async () => {
    try {
      // بيانات وهمية للفريق
      const mockData = [
        {
          id: 1,
          name: 'أحمد محمد علي',
          position: 'المحامي الرئيسي',
          email: '<EMAIL>',
          phone: '+20 2 1234 5678',
          specialization: 'قانون الشركات',
          experience: '15 سنة',
          image: '/images/team/lawyer1.jpg',
          bio: 'محامي متخصص في قانون الشركات والتجارة الدولية',
          status: 'active'
        },
        {
          id: 2,
          name: 'فاطمة أحمد حسن',
          position: 'محامية أولى',
          email: '<EMAIL>',
          phone: '+20 2 1234 5679',
          specialization: 'القانون المدني',
          experience: '12 سنة',
          image: '/images/team/lawyer2.jpg',
          bio: 'محامية متخصصة في القانون المدني وحقوق الأسرة',
          status: 'active'
        },
        {
          id: 3,
          name: 'محمد عبد الرحمن',
          position: 'محامي متدرب',
          email: '<EMAIL>',
          phone: '+20 2 1234 5680',
          specialization: 'القانون الجنائي',
          experience: '3 سنوات',
          image: '/images/team/lawyer3.jpg',
          bio: 'محامي متدرب متخصص في القانون الجنائي',
          status: 'active'
        }
      ];
      
      setTeamMembers(mockData);
      setLoading(false);
    } catch (error) {
      console.error('خطأ في جلب بيانات الفريق:', error);
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      // هنا يمكن إضافة API call للحذف
      setTeamMembers(prev => prev.filter(member => member.id !== id));
      setDeleteConfirm(null);
    } catch (error) {
      console.error('خطأ في حذف العضو:', error);
    }
  };

  const filteredMembers = teamMembers.filter(member =>
    member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.specialization.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <AdminLayout title="إدارة الفريق">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-900 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري التحميل...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="إدارة الفريق">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة الفريق</h1>
            <p className="text-gray-600">إدارة أعضاء فريق المكتب</p>
          </div>
          <Link href="/admin/team/new">
            <button className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors duration-200 flex items-center font-medium">
              <FaPlus className="ml-2" />
              إضافة عضو جديد
            </button>
          </Link>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="البحث في الفريق..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Team Members Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMembers.map((member) => (
            <div key={member.id} className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300">
              {/* Member Image */}
              <div className="h-48 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center">
                {member.image ? (
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <FaUser className="text-6xl text-primary-400" />
                )}
              </div>

              {/* Member Info */}
              <div className="p-6">
                <div className="mb-4">
                  <h3 className="text-xl font-bold text-gray-900 mb-1">{member.name}</h3>
                  <p className="text-primary-600 font-medium">{member.position}</p>
                  <p className="text-sm text-gray-600">{member.specialization}</p>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <FaEnvelope className="ml-2 text-gray-400" />
                    <span>{member.email}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <FaPhone className="ml-2 text-gray-400" />
                    <span>{member.phone}</span>
                  </div>
                </div>

                <div className="mb-4">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    نشط
                  </span>
                  <span className="mr-2 text-sm text-gray-600">خبرة: {member.experience}</span>
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  <button className="flex-1 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200 flex items-center justify-center text-sm">
                    <FaEdit className="ml-1" />
                    تعديل
                  </button>
                  <button className="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200 flex items-center justify-center text-sm">
                    <FaEye className="ml-1" />
                    عرض
                  </button>
                  <button
                    onClick={() => setDeleteConfirm(member.id)}
                    className="bg-red-100 text-red-700 px-3 py-2 rounded-lg hover:bg-red-200 transition-colors duration-200 flex items-center justify-center"
                  >
                    <FaTrash />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredMembers.length === 0 && (
          <div className="text-center py-12">
            <FaUser className="text-6xl text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">لا يوجد أعضاء فريق</h3>
            <p className="text-gray-600 mb-6">ابدأ بإضافة أعضاء الفريق لإدارة المكتب</p>
            <Link href="/admin/team/new">
              <button className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors duration-200">
                إضافة عضو جديد
              </button>
            </Link>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {deleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">تأكيد الحذف</h3>
              <p className="text-gray-600 mb-6">هل أنت متأكد من حذف هذا العضو؟ لا يمكن التراجع عن هذا الإجراء.</p>
              <div className="flex gap-3">
                <button
                  onClick={() => handleDelete(deleteConfirm)}
                  className="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors duration-200"
                >
                  حذف
                </button>
                <button
                  onClick={() => setDeleteConfirm(null)}
                  className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors duration-200"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
