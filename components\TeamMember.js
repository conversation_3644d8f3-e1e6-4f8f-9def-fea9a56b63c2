import { Fa<PERSON>inkedin, FaEnvelope, FaPhone } from 'react-icons/fa';

function TeamMember({ name, position, image, bio, specializations, email, phone, linkedin }) {
  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden card-hover group">
      {/* Image */}
      <div className="relative overflow-hidden">
        <img
          src={image || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'}
          alt={name}
          className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* Social Links Overlay */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-3 space-x-reverse opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          {email && (
            <a
              href={`mailto:${email}`}
              className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-gold-500 transition-colors duration-200"
            >
              <FaEnvelope size={16} />
            </a>
          )}
          {phone && (
            <a
              href={`tel:${phone}`}
              className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-gold-500 transition-colors duration-200"
            >
              <FaPhone size={16} />
            </a>
          )}
          {linkedin && (
            <a
              href={linkedin}
              target="_blank"
              rel="noopener noreferrer"
              className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-gold-500 transition-colors duration-200"
            >
              <FaLinkedin size={16} />
            </a>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Name and Position */}
        <h3 className="text-xl font-bold text-primary-900 mb-1">{name}</h3>
        <p className="text-gold-600 font-semibold mb-3">{position}</p>

        {/* Bio */}
        {bio && (
          <p className="text-gray-600 text-sm leading-relaxed mb-4">
            {bio}
          </p>
        )}

        {/* Specializations */}
        {specializations && specializations.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-primary-900 mb-2">مجالات التخصص:</h4>
            <div className="flex flex-wrap gap-2">
              {specializations.map((spec, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                >
                  {spec}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Contact Info */}
        <div className="space-y-2 text-sm text-gray-600">
          {email && (
            <div className="flex items-center">
              <FaEnvelope className="ml-2 text-gold-500" />
              <a href={`mailto:${email}`} className="hover:text-primary-900 transition-colors duration-200">
                {email}
              </a>
            </div>
          )}
          {phone && (
            <div className="flex items-center">
              <FaPhone className="ml-2 text-gold-500" />
              <a href={`tel:${phone}`} className="hover:text-primary-900 transition-colors duration-200">
                {phone}
              </a>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default TeamMember;
