export const errorHandler = (handler) => {
  return async (req, res) => {
    try {
      return await handler(req, res);
    } catch (error) {
      console.error('API Error:', error);

      // تحديد نوع الخطأ وإرجاع الاستجابة المناسبة
      if (error.name === 'ValidationError') {
        return res.status(400).json({
          success: false,
          message: 'بيانات غير صحيحة',
          errors: error.errors
        });
      }

      if (error.name === 'UnauthorizedError') {
        return res.status(401).json({
          success: false,
          message: 'غير مصرح لك بالوصول'
        });
      }

      if (error.name === 'ForbiddenError') {
        return res.status(403).json({
          success: false,
          message: 'ليس لديك صلاحية للقيام بهذا الإجراء'
        });
      }

      if (error.name === 'NotFoundError') {
        return res.status(404).json({
          success: false,
          message: 'المورد المطلوب غير موجود'
        });
      }

      if (error.name === 'ConflictError') {
        return res.status(409).json({
          success: false,
          message: 'تعارض في البيانات'
        });
      }

      // خطأ عام في الخادم
      return res.status(500).json({
        success: false,
        message: 'حدث خطأ في الخادم',
        ...(process.env.NODE_ENV === 'development' && { 
          error: error.message,
          stack: error.stack 
        })
      });
    }
  };
};

// دوال مساعدة لإنشاء أخطاء مخصصة
export class ValidationError extends Error {
  constructor(message, errors = {}) {
    super(message);
    this.name = 'ValidationError';
    this.errors = errors;
  }
}

export class UnauthorizedError extends Error {
  constructor(message = 'غير مصرح لك بالوصول') {
    super(message);
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends Error {
  constructor(message = 'ليس لديك صلاحية للقيام بهذا الإجراء') {
    super(message);
    this.name = 'ForbiddenError';
  }
}

export class NotFoundError extends Error {
  constructor(message = 'المورد المطلوب غير موجود') {
    super(message);
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends Error {
  constructor(message = 'تعارض في البيانات') {
    super(message);
    this.name = 'ConflictError';
  }
}

// دالة للتحقق من صحة البيانات
export const validateRequired = (data, requiredFields) => {
  const errors = {};
  
  for (const field of requiredFields) {
    if (!data[field] || (typeof data[field] === 'string' && !data[field].trim())) {
      errors[field] = `${field} مطلوب`;
    }
  }
  
  if (Object.keys(errors).length > 0) {
    throw new ValidationError('بيانات مطلوبة مفقودة', errors);
  }
};

// دالة للتحقق من طول النص
export const validateLength = (data, field, minLength, maxLength) => {
  const value = data[field];
  
  if (value && typeof value === 'string') {
    if (minLength && value.length < minLength) {
      throw new ValidationError(`${field} يجب أن يكون ${minLength} أحرف على الأقل`);
    }
    
    if (maxLength && value.length > maxLength) {
      throw new ValidationError(`${field} يجب أن يكون أقل من ${maxLength} حرف`);
    }
  }
};

// دالة للتحقق من البريد الإلكتروني
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!emailRegex.test(email)) {
    throw new ValidationError('البريد الإلكتروني غير صحيح');
  }
};

// دالة للتحقق من الرابط المختصر
export const validateSlug = (slug) => {
  const slugRegex = /^[a-z0-9-]+$/;
  
  if (!slugRegex.test(slug)) {
    throw new ValidationError('الرابط المختصر يجب أن يحتوي على أحرف إنجليزية صغيرة وأرقام وشرطات فقط');
  }
};
