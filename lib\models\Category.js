import mongoose from 'mongoose';

const categorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'اسم الفئة مطلوب'],
    trim: true,
    maxlength: [100, 'اسم الفئة يجب أن يكون أقل من 100 حرف']
  },
  slug: {
    type: String,
    required: [true, 'الرابط المختصر مطلوب'],
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^[a-z0-9-]+$/, 'الرابط المختصر يجب أن يحتوي على أحرف إنجليزية وأرقام وشرطات فقط']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'الوصف يجب أن يكون أقل من 500 حرف']
  },
  color: {
    type: String,
    trim: true,
    match: [/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'لون غير صحيح'],
    default: '#3B82F6'
  },
  icon: {
    type: String,
    trim: true
  },
  image: {
    type: String,
    trim: true
  },
  parent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    default: null
  },
  order: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  type: {
    type: String,
    enum: ['article', 'service', 'general'],
    default: 'general'
  },
  meta: {
    title: {
      type: String,
      trim: true,
      maxlength: [60, 'عنوان SEO يجب أن يكون أقل من 60 حرف']
    },
    description: {
      type: String,
      trim: true,
      maxlength: [160, 'وصف SEO يجب أن يكون أقل من 160 حرف']
    },
    keywords: {
      type: String,
      trim: true
    }
  },
  stats: {
    articlesCount: {
      type: Number,
      default: 0
    },
    servicesCount: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
});

// فهرسة للبحث والأداء
categorySchema.index({ slug: 1 }, { unique: true });
categorySchema.index({ type: 1 });
categorySchema.index({ isActive: 1 });
categorySchema.index({ parent: 1 });
categorySchema.index({ order: 1 });
categorySchema.index({ name: 'text', description: 'text' });

// Virtual للحصول على الفئات الفرعية
categorySchema.virtual('children', {
  ref: 'Category',
  localField: '_id',
  foreignField: 'parent'
});

// Virtual للحصول على عدد العناصر الإجمالي
categorySchema.virtual('totalCount').get(function() {
  return this.stats.articlesCount + this.stats.servicesCount;
});

// طرق النموذج
categorySchema.methods.updateArticlesCount = async function() {
  const Article = mongoose.model('Article');
  const count = await Article.countDocuments({ category: this.name, status: 'published' });
  this.stats.articlesCount = count;
  return this.save();
};

categorySchema.methods.updateServicesCount = async function() {
  const Service = mongoose.model('Service');
  const count = await Service.countDocuments({ category: this.name, status: 'published' });
  this.stats.servicesCount = count;
  return this.save();
};

categorySchema.methods.updateAllCounts = async function() {
  await this.updateArticlesCount();
  await this.updateServicesCount();
  return this;
};

// طرق ثابتة
categorySchema.statics.findActive = function() {
  return this.find({ isActive: true }).sort({ order: 1, name: 1 });
};

categorySchema.statics.findByType = function(type) {
  return this.find({ type, isActive: true }).sort({ order: 1, name: 1 });
};

categorySchema.statics.findParents = function() {
  return this.find({ parent: null, isActive: true }).sort({ order: 1, name: 1 });
};

categorySchema.statics.findChildren = function(parentId) {
  return this.find({ parent: parentId, isActive: true }).sort({ order: 1, name: 1 });
};

categorySchema.statics.getHierarchy = async function(type = null) {
  const query = { parent: null, isActive: true };
  if (type) query.type = type;
  
  const parents = await this.find(query)
    .populate('children')
    .sort({ order: 1, name: 1 });
  
  return parents;
};

categorySchema.statics.updateAllStats = async function() {
  const categories = await this.find({});
  
  for (const category of categories) {
    await category.updateAllCounts();
  }
  
  return true;
};

// Middleware لتحديث الإحصائيات عند الحذف
categorySchema.pre('deleteOne', { document: true, query: false }, async function() {
  // تحديث الفئات الفرعية لتصبح بدون والد
  await this.constructor.updateMany(
    { parent: this._id },
    { parent: null }
  );
});

// تصدير النموذج
const Category = mongoose.models.Category || mongoose.model('Category', categorySchema);

export default Category;
