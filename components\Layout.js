import Head from 'next/head';
import Header from './Header';
import Footer from './Footer';

const Layout = ({ children, title = 'مكتب المحاماة للاستشارات القانونية', description = 'مكتب محاماة متخصص في تقديم الاستشارات القانونية والخدمات القانونية المتكاملة' }) => {
  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content={description} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="keywords" content="محاماة, استشارات قانونية, قانون, محامي, خدمات قانونية" />
        <meta name="author" content="مكتب المحاماة للاستشارات القانونية" />
        <meta property="og:title" content={title} />
        <meta property="og:description" content={description} />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={title} />
        <meta name="twitter:description" content={description} />
        <link rel="icon" href="/favicon.ico" />
        <link rel="canonical" href="https://lawfirm.com" />
      </Head>
      <div className="main-site min-h-screen flex flex-col">
        <Header />
        <main className="flex-grow">
          {children}
        </main>
        <Footer />
      </div>
    </>
  );
};

export default Layout;
