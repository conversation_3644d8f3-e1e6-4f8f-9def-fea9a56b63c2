import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '../../../../components/admin/AdminLayout';
import WYSIWYGEditor from '../../../../components/admin/WYSIWYGEditor';
import Link from 'next/link';
import {
  FaSave,
  FaArrowLeft,
  FaEye,
  FaImage,
  FaCalendar,
  FaUser,
  FaTag,
  FaExclamationTriangle,
  FaCheckCircle,
  FaUpload,
  FaTimes,
  FaFilePdf
} from 'react-icons/fa';
import { normalizeTagsToString } from '../../../../utils/tagHelpers';

export default function EditArticle() {
  const router = useRouter();
  const { id } = router.query;
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [validationErrors, setValidationErrors] = useState({});
  const [imagePreview, setImagePreview] = useState('');
  const [imageUploading, setImageUploading] = useState(false);
  const [pdfUploading, setPdfUploading] = useState(false);
  const fileInputRef = useRef(null);
  
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    image: '',
    author: '',
    category: '',
    tags: '',
    status: 'published',
    featured: false,
    pdf_file: null
  });
  const [categories, setCategories] = useState([]);

  useEffect(() => {
    if (id) {
      fetchArticle();
    }
    fetchCategories();
  }, [id]);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      const data = await response.json();
      if (data.success) {
        setCategories(data.categories);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchArticle = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/articles/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      
      if (data.success) {
        const article = data.article;
        setFormData({
          ...article,
          tags: normalizeTagsToString(article.tags) // تحويل tags إلى string للنموذج
        });

        // تحديد معاينة الصورة
        if (article.image) {
          setImagePreview(article.image);
        }
      } else {
        setError(data.message);
      }
    } catch (error) {
      setError('حدث خطأ في جلب بيانات المقال');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // مسح رسالة الخطأ عند التعديل
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleContentChange = (content) => {
    setFormData(prev => ({
      ...prev,
      content
    }));

    if (validationErrors.content) {
      setValidationErrors(prev => ({
        ...prev,
        content: ''
      }));
    }
  };

  const generateSlug = (title) => {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim();
  };

  const handleTitleChange = (e) => {
    const title = e.target.value;
    setFormData(prev => ({
      ...prev,
      title,
      slug: generateSlug(title)
    }));

    if (validationErrors.title) {
      setValidationErrors(prev => ({
        ...prev,
        title: ''
      }));
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.title.trim()) {
      errors.title = 'عنوان المقال مطلوب';
    }

    if (!formData.content.trim()) {
      errors.content = 'محتوى المقال مطلوب';
    }

    if (!formData.author.trim()) {
      errors.author = 'اسم الكاتب مطلوب';
    }

    if (!formData.category) {
      errors.category = 'تصنيف المقال مطلوب';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
      setError('يُسمح فقط بملفات الصور');
      return;
    }

    // التحقق من حجم الملف (5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('حجم الصورة يجب أن يكون أقل من 5 ميجابايت');
      return;
    }

    setImageUploading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        setFormData(prev => ({
          ...prev,
          image: result.image.url
        }));
        setImagePreview(result.image.url);
        setSuccess('تم رفع الصورة بنجاح');
      } else {
        setError(result.error || 'خطأ في رفع الصورة');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setError('خطأ في رفع الصورة');
    } finally {
      setImageUploading(false);
    }
  };

  const removeImage = () => {
    setFormData(prev => ({
      ...prev,
      image: ''
    }));
    setImagePreview('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handlePDFUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (file.type !== 'application/pdf') {
      setError('يُسمح فقط بملفات PDF');
      return;
    }

    // التحقق من حجم الملف (20MB)
    if (file.size > 20 * 1024 * 1024) {
      setError('حجم الملف يجب أن يكون أقل من 20 ميجابايت');
      return;
    }

    setPdfUploading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('pdf', file);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000);

      const response = await fetch('/api/upload/pdf', {
        method: 'POST',
        body: formData,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const result = await response.json();

      if (result.success) {
        setFormData(prev => ({
          ...prev,
          pdf_file: result.file
        }));
        setSuccess('تم رفع ملف PDF بنجاح');
      } else {
        setError(result.error || 'خطأ في رفع الملف');
      }
    } catch (error) {
      console.error('Upload error:', error);
      if (error.name === 'AbortError') {
        setError('انتهت مهلة رفع الملف. يرجى المحاولة مرة أخرى.');
      } else if (error.message.includes('Failed to fetch')) {
        setError('خطأ في الاتصال. تحقق من الإنترنت وحاول مرة أخرى.');
      } else {
        setError('خطأ في رفع الملف: ' + (error.message || 'خطأ غير معروف'));
      }
    } finally {
      setPdfUploading(false);
    }
  };

  const removePDF = () => {
    setFormData(prev => ({
      ...prev,
      pdf_file: null
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      setError('يرجى تصحيح الأخطاء المذكورة أعلاه');
      return;
    }

    setSaving(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/articles/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('تم تحديث المقال بنجاح');
        setTimeout(() => {
          router.push('/admin/articles');
        }, 2000);
      } else {
        setError(data.message || 'حدث خطأ في تحديث المقال');
      }
    } catch (error) {
      setError('حدث خطأ في الاتصال بالخادم');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout title="تعديل المقال">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-900 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري التحميل...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="تعديل المقال">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">تعديل المقال</h1>
            <p className="text-gray-600">تحديث بيانات ومحتوى المقال</p>
          </div>
          <Link href="/admin/articles">
            <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
              <FaArrowLeft />
              العودة إلى المقالات
            </button>
          </Link>
        </div>

        {/* Messages */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded flex items-center">
            <FaExclamationTriangle className="ml-2" />
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded flex items-center">
            <FaCheckCircle className="ml-2" />
            {success}
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Title */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">معلومات المقال الأساسية</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      عنوان المقال *
                    </label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleTitleChange}
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                        validationErrors.title ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="أدخل عنوان المقال..."
                    />
                    {validationErrors.title && (
                      <p className="mt-2 text-sm text-red-600 flex items-center">
                        <FaExclamationTriangle className="ml-1" />
                        {validationErrors.title}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الرابط المختصر (Slug)
                    </label>
                    <input
                      type="text"
                      name="slug"
                      value={formData.slug}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="article-slug"
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      سيتم إنشاؤه تلقائياً من العنوان إذا تُرك فارغاً
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      ملخص المقال
                    </label>
                    <textarea
                      name="excerpt"
                      value={formData.excerpt}
                      onChange={handleInputChange}
                      rows={3}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="ملخص قصير عن المقال..."
                    />
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">محتوى المقال</h2>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    محتوى المقال *
                  </label>
                  <div className={`rounded-xl overflow-hidden ${
                    validationErrors.content
                      ? 'ring-2 ring-red-500'
                      : formData.content
                      ? 'ring-2 ring-green-500'
                      : ''
                  }`}>
                    <WYSIWYGEditor
                      value={formData.content}
                      onChange={handleContentChange}
                      placeholder="ابدأ في كتابة محتوى المقال..."
                      height={500}
                    />
                  </div>
                  {validationErrors.content && (
                    <p className="mt-2 text-sm text-red-600 flex items-center">
                      <FaExclamationTriangle className="ml-1" />
                      {validationErrors.content}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Publish Settings */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">إعدادات النشر</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      حالة المقال
                    </label>
                    <select
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="published">منشور</option>
                      <option value="draft">مسودة</option>
                    </select>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      name="featured"
                      checked={formData.featured}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <label className="mr-2 block text-sm text-gray-900">
                      مقال مميز
                    </label>
                  </div>
                </div>
              </div>

              {/* Image Upload */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <FaImage className="ml-2 text-primary-600" />
                  الصورة المميزة
                </h3>

                <div className="space-y-4">
                  {imagePreview ? (
                    <div className="relative group">
                      <img
                        src={imagePreview}
                        alt="معاينة الصورة"
                        className="w-full h-48 object-cover rounded-lg"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                        <button
                          type="button"
                          onClick={removeImage}
                          className="bg-red-500 text-white p-3 rounded-full hover:bg-red-600 transition-colors duration-200"
                        >
                          <FaTimes />
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-500 transition-colors duration-200 cursor-pointer">
                      <FaImage className="text-4xl text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 mb-4">اسحب الصورة هنا أو انقر للاختيار</p>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                        id="image-upload"
                        disabled={imageUploading}
                      />
                      <label
                        htmlFor="image-upload"
                        className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors duration-200 cursor-pointer ${
                          imageUploading
                            ? 'bg-gray-400 text-white cursor-not-allowed'
                            : 'bg-primary-600 text-white hover:bg-primary-700'
                        }`}
                      >
                        {imageUploading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                            جاري الرفع...
                          </>
                        ) : (
                          <>
                            <FaUpload className="ml-2" />
                            اختر صورة
                          </>
                        )}
                      </label>
                    </div>
                  )}

                  <p className="text-xs text-gray-500">
                    الحد الأقصى: 5 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF
                  </p>
                </div>
              </div>

              {/* PDF Upload */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <FaFilePdf className="ml-2 text-red-600" />
                  ملف PDF مرفق
                </h3>

                {!formData.pdf_file ? (
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors duration-200">
                    <FaFilePdf className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <p className="text-gray-600 mb-4">اختر ملف PDF لإرفاقه مع المقال</p>
                    <input
                      type="file"
                      accept=".pdf"
                      onChange={handlePDFUpload}
                      className="hidden"
                      id="pdf-upload"
                      disabled={pdfUploading}
                    />
                    <label
                      htmlFor="pdf-upload"
                      className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors duration-200 cursor-pointer ${
                        pdfUploading
                          ? 'bg-gray-400 text-white cursor-not-allowed'
                          : 'bg-red-600 text-white hover:bg-red-700'
                      }`}
                    >
                      {pdfUploading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                          جاري الرفع...
                        </>
                      ) : (
                        <>
                          <FaUpload className="ml-2" />
                          اختر ملف PDF
                        </>
                      )}
                    </label>
                    <p className="text-xs text-gray-500 mt-2">الحد الأقصى: 20 ميجابايت</p>
                  </div>
                ) : (
                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <FaFilePdf className="text-red-600 text-xl" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">{formData.pdf_file.name}</p>
                          <p className="text-xs text-gray-500">
                            {(formData.pdf_file.size / 1024 / 1024).toFixed(2)} ميجابايت
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <a
                          href={formData.pdf_file.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 text-blue-600 hover:bg-blue-100 rounded-full transition-colors"
                          title="معاينة"
                        >
                          <FaEye />
                        </a>
                        <button
                          onClick={removePDF}
                          className="p-2 text-red-600 hover:bg-red-100 rounded-full transition-colors"
                          title="حذف"
                        >
                          <FaTimes />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Article Details */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">تفاصيل المقال</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <FaUser className="inline ml-1" />
                      الكاتب *
                    </label>
                    <input
                      type="text"
                      name="author"
                      value={formData.author}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                        validationErrors.author ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="اسم الكاتب"
                    />
                    {validationErrors.author && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.author}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <FaTag className="inline ml-1" />
                      التصنيف *
                    </label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                        validationErrors.category ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value="">اختر التصنيف</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.name}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                    {validationErrors.category && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.category}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الكلمات المفتاحية
                    </label>
                    <input
                      type="text"
                      name="tags"
                      value={formData.tags}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="كلمة1، كلمة2، كلمة3"
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      افصل بين الكلمات بفاصلة
                    </p>
                  </div>


                </div>
              </div>

              {/* Actions */}
              <div className="bg-white rounded-lg shadow p-6">
                <div className="space-y-3">
                  <button
                    type="submit"
                    disabled={saving}
                    className="w-full bg-primary-900 text-white px-6 py-3 rounded-lg hover:bg-primary-800 transition-colors duration-200 flex items-center justify-center disabled:opacity-50"
                  >
                    {saving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                        جاري الحفظ...
                      </>
                    ) : (
                      <>
                        <FaSave className="ml-2" />
                        تحديث المقال
                      </>
                    )}
                  </button>

                  {formData.status === 'published' && (
                    <Link href={`/articles/${formData.slug}`} target="_blank">
                      <button
                        type="button"
                        className="w-full bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center justify-center"
                      >
                        <FaEye className="ml-2" />
                        معاينة المقال
                      </button>
                    </Link>
                  )}
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
}
