import { readData } from '../../../lib/database';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }

  try {
    const { page = 1, limit = 10, search = '', category = '', featured = '' } = req.query;

    let services = await readData('services');

    // التأكد من أن services هو array
    if (!Array.isArray(services)) {
      services = [];
    }

    // عرض الخدمات المنشورة فقط
    services = services.filter(service => service.status === 'published');
    
    // البحث
    if (search) {
      services = services.filter(service => 
        service.title.toLowerCase().includes(search.toLowerCase()) ||
        service.description.toLowerCase().includes(search.toLowerCase()) ||
        service.content.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    // فلترة حسب التصنيف
    if (category) {
      services = services.filter(service => service.category === category);
    }
    
    // فلترة الخدمات المميزة
    if (featured === 'true') {
      services = services.filter(service => service.featured === true);
    }
    
    // ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
    services.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    
    // تقسيم الصفحات
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedServices = services.slice(startIndex, endIndex);
    
    return res.status(200).json({
      success: true,
      services: paginatedServices,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: services.length,
        pages: Math.ceil(services.length / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching services:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الخدمات'
    });
  }
}
