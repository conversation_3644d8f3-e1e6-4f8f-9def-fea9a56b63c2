import mongoose from 'mongoose';

const pageSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'عنوان الصفحة مطلوب'],
    trim: true,
    maxlength: [200, 'العنوان يجب أن يكون أقل من 200 حرف']
  },
  slug: {
    type: String,
    required: [true, 'الرابط المختصر مطلوب'],
    trim: true,
    lowercase: true,
    match: [/^[a-z0-9-]+$/, 'الرابط المختصر يجب أن يحتوي على أحرف إنجليزية وأرقام وشرطات فقط']
  },
  content: {
    type: String,
    required: [true, 'محتوى الصفحة مطلوب']
  },
  excerpt: {
    type: String,
    trim: true,
    maxlength: [500, 'المقتطف يجب أن يكون أقل من 500 حرف']
  },
  featured_image: {
    type: String,
    trim: true
  },
  template: {
    type: String,
    enum: ['default', 'full-width', 'sidebar', 'landing', 'contact'],
    default: 'default'
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'private', 'archived'],
    default: 'draft'
  },
  parent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Page',
    default: null
  },
  order: {
    type: Number,
    default: 0
  },
  showInMenu: {
    type: Boolean,
    default: true
  },
  menuTitle: {
    type: String,
    trim: true,
    maxlength: [100, 'عنوان القائمة يجب أن يكون أقل من 100 حرف']
  },
  views: {
    type: Number,
    default: 0
  },
  meta: {
    title: {
      type: String,
      trim: true,
      maxlength: [60, 'عنوان SEO يجب أن يكون أقل من 60 حرف']
    },
    description: {
      type: String,
      trim: true,
      maxlength: [160, 'وصف SEO يجب أن يكون أقل من 160 حرف']
    },
    keywords: {
      type: String,
      trim: true
    },
    robots: {
      type: String,
      enum: ['index,follow', 'noindex,follow', 'index,nofollow', 'noindex,nofollow'],
      default: 'index,follow'
    }
  },
  customFields: [{
    key: {
      type: String,
      required: true,
      trim: true
    },
    value: {
      type: mongoose.Schema.Types.Mixed
    },
    type: {
      type: String,
      enum: ['text', 'textarea', 'number', 'boolean', 'date', 'url', 'email'],
      default: 'text'
    }
  }],
  publishedAt: {
    type: Date
  },
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
});

// فهرسة للبحث والأداء
pageSchema.index({ slug: 1 }, { unique: true });
pageSchema.index({ status: 1 });
pageSchema.index({ parent: 1 });
pageSchema.index({ showInMenu: 1 });
pageSchema.index({ order: 1 });
pageSchema.index({ publishedAt: -1 });
pageSchema.index({ 
  title: 'text', 
  content: 'text',
  excerpt: 'text'
}, {
  weights: {
    title: 10,
    excerpt: 5,
    content: 1
  }
});

// Virtual للحصول على الصفحات الفرعية
pageSchema.virtual('children', {
  ref: 'Page',
  localField: '_id',
  foreignField: 'parent'
});

// Virtual للحصول على عنوان القائمة
pageSchema.virtual('displayTitle').get(function() {
  return this.menuTitle || this.title;
});

// تحديث تاريخ النشر عند تغيير الحالة إلى منشور
pageSchema.pre('save', function(next) {
  if (this.isModified('status') && this.status === 'published' && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  next();
});

// طرق النموذج
pageSchema.methods.incrementViews = function() {
  return this.updateOne({ $inc: { views: 1 } });
};

pageSchema.methods.getCustomField = function(key) {
  const field = this.customFields.find(f => f.key === key);
  return field ? field.value : null;
};

pageSchema.methods.setCustomField = function(key, value, type = 'text') {
  const existingIndex = this.customFields.findIndex(f => f.key === key);
  
  if (existingIndex >= 0) {
    this.customFields[existingIndex].value = value;
    this.customFields[existingIndex].type = type;
  } else {
    this.customFields.push({ key, value, type });
  }
  
  return this.save();
};

// طرق ثابتة
pageSchema.statics.findPublished = function() {
  return this.find({ status: 'published' }).sort({ order: 1, title: 1 });
};

pageSchema.statics.findForMenu = function() {
  return this.find({ 
    status: 'published', 
    showInMenu: true,
    parent: null
  })
  .populate('children')
  .sort({ order: 1, title: 1 });
};

pageSchema.statics.findByTemplate = function(template) {
  return this.find({ status: 'published', template }).sort({ order: 1, title: 1 });
};

pageSchema.statics.searchPages = function(query) {
  return this.find(
    { 
      $text: { $search: query },
      status: 'published'
    },
    { score: { $meta: 'textScore' } }
  ).sort({ score: { $meta: 'textScore' } });
};

pageSchema.statics.getHierarchy = async function() {
  const parents = await this.find({ 
    status: 'published',
    parent: null 
  })
  .populate({
    path: 'children',
    match: { status: 'published' },
    options: { sort: { order: 1, title: 1 } }
  })
  .sort({ order: 1, title: 1 });
  
  return parents;
};

pageSchema.statics.reorderPages = async function(pageIds) {
  const updates = pageIds.map((id, index) => ({
    updateOne: {
      filter: { _id: id },
      update: { order: index }
    }
  }));
  
  return this.bulkWrite(updates);
};

// تصدير النموذج
const Page = mongoose.models.Page || mongoose.model('Page', pageSchema);

export default Page;
