import jwt from 'jsonwebtoken';
import { getUserFromToken } from '../lib/auth';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

export const requireAdmin = (handler) => {
  return async (req, res) => {
    try {
      // استخراج التوكن من الهيدر
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
          success: false,
          message: 'غير مصرح لك بالوصول'
        });
      }

      const token = authHeader.substring(7); // إزالة "Bearer "

      // التحقق من صحة التوكن والحصول على المستخدم
      const user = getUserFromToken(token);

      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'التوكن غير صالح أو المستخدم غير موجود'
        });
      }

      // التحقق من صلاحيات الإدارة
      if (user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'ليس لديك صلاحيات إدارية'
        });
      }

      // إضافة معلومات المستخدم إلى الطلب
      req.user = user;

      // تنفيذ المعالج الأصلي
      return handler(req, res);
    } catch (error) {
      console.error('Auth middleware error:', error);
      return res.status(500).json({
        success: false,
        message: 'خطأ في التحقق من الصلاحيات'
      });
    }
  };
};

export const optionalAuth = (handler) => {
  return async (req, res) => {
    try {
      const authHeader = req.headers.authorization;
      
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);

        try {
          const user = getUserFromToken(token);

          if (user) {
            req.user = user;
          }
        } catch (error) {
          // التوكن غير صالح، لكن نستمر بدون مستخدم
          console.log('Invalid token in optional auth:', error.message);
        }
      }

      return handler(req, res);
    } catch (error) {
      console.error('Optional auth middleware error:', error);
      return handler(req, res);
    }
  };
};

// تم نقل generateToken و verifyToken إلى lib/auth.js لتجنب التكرار
