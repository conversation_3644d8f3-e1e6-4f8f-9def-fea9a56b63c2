import Head from 'next/head';
import LocalizedHeader from './LocalizedHeader';
import Footer from './Footer';
import { getDirection } from '../lib/i18n';

const LocalizedLayout = ({ 
  children, 
  locale = 'ar',
  title = 'مكتب المحاماة للاستشارات القانونية', 
  description = 'مكتب محاماة متخصص في تقديم الاستشارات القانونية والخدمات القانونية المتكاملة' 
}) => {
  const dir = getDirection(locale);
  
  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content={description} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="keywords" content="محاماة, استشارات قانونية, قانون, محامي, خدمات قانونية" />
        <meta name="author" content="مكتب المحاماة للاستشارات القانونية" />
        <meta property="og:title" content={title} />
        <meta property="og:description" content={description} />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={title} />
        <meta name="twitter:description" content={description} />
        <link rel="icon" href="/favicon.ico" />
        <link rel="canonical" href={`https://lawfirm.com/${locale}`} />
        
        {/* Language alternates */}
        <link rel="alternate" hrefLang="ar" href={`https://lawfirm.com/ar`} />
        <link rel="alternate" hrefLang="en" href={`https://lawfirm.com/en`} />
        <link rel="alternate" hrefLang="x-default" href={`https://lawfirm.com/ar`} />
      </Head>
      <div className={`main-site min-h-screen flex flex-col`} dir={dir}>
        <LocalizedHeader locale={locale} />
        <main className="flex-grow">
          {children}
        </main>
        <Footer locale={locale} />
      </div>
    </>
  );
};

export default LocalizedLayout;
