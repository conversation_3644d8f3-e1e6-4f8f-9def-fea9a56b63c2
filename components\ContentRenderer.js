import { useEffect, useRef } from 'react';

const ContentRenderer = ({ content, className = '' }) => {
  const contentRef = useRef(null);

  useEffect(() => {
    if (contentRef.current && content) {
      // تحويل عناصر YouTube placeholder إلى فيديوهات حقيقية
      const processYouTubeVideos = () => {
        const youtubeElements = contentRef.current.querySelectorAll('p');
        
        youtubeElements.forEach(element => {
          const text = element.textContent || element.innerText;
          
          // البحث عن معرف فيديو YouTube في النص
          if (text.includes('📺') && text.includes('معرف الفيديو:')) {
            const videoIdMatch = text.match(/معرف الفيديو:\s*([a-zA-Z0-9_-]+)/);
            
            if (videoIdMatch && videoIdMatch[1]) {
              const videoId = videoIdMatch[1];
              
              // إنشاء عنصر فيديو YouTube
              const videoContainer = document.createElement('div');
              videoContainer.className = 'youtube-video-container';
              videoContainer.style.cssText = `
                position: relative;
                width: 100%;
                max-width: 800px;
                margin: 20px auto;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                background: #000;
              `;
              
              // إنشاء wrapper للـ iframe
              const iframeWrapper = document.createElement('div');
              iframeWrapper.style.cssText = `
                position: relative;
                width: 100%;
                height: 0;
                padding-bottom: 56.25%;
              `;
              
              // إنشاء iframe
              const iframe = document.createElement('iframe');
              iframe.src = `https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1&showinfo=0`;
              iframe.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border: none;
              `;
              iframe.allowFullscreen = true;
              iframe.title = 'فيديو YouTube';
              iframe.loading = 'lazy';
              
              // إنشاء شارة YouTube
              const badge = document.createElement('div');
              badge.style.cssText = `
                position: absolute;
                top: 8px;
                right: 8px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 6px 10px;
                border-radius: 6px;
                font-size: 12px;
                font-family: Arial, sans-serif;
                display: flex;
                align-items: center;
                gap: 4px;
                z-index: 10;
              `;
              badge.innerHTML = '<span style="color: #ff0000;">📺</span> YouTube';
              
              // تجميع العناصر
              iframeWrapper.appendChild(iframe);
              videoContainer.appendChild(iframeWrapper);
              videoContainer.appendChild(badge);
              
              // استبدال العنصر القديم
              element.parentNode.replaceChild(videoContainer, element);
            }
          }
        });
      };

      // معالجة المحتوى
      contentRef.current.innerHTML = content;
      
      // تحويل فيديوهات YouTube
      setTimeout(processYouTubeVideos, 100);
    }
  }, [content]);

  return (
    <div 
      ref={contentRef}
      className={`content-renderer ${className}`}
      style={{
        lineHeight: '1.8',
        fontSize: '16px',
        color: '#333'
      }}
    />
  );
};

export default ContentRenderer;
