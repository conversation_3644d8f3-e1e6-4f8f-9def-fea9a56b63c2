import mongoose from 'mongoose';

const articleSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'عنوان المقال مطلوب'],
    trim: true,
    maxlength: [200, 'العنوان يجب أن يكون أقل من 200 حرف']
  },
  slug: {
    type: String,
    required: [true, 'الرابط المختصر مطلوب'],
    trim: true,
    lowercase: true,
    match: [/^[a-z0-9-]+$/, 'الرابط المختصر يجب أن يحتوي على أحرف إنجليزية وأرقام وشرطات فقط']
  },
  excerpt: {
    type: String,
    required: [true, 'المقتطف مطلوب'],
    trim: true,
    maxlength: [500, 'المقتطف يجب أن يكون أقل من 500 حرف']
  },
  content: {
    type: String,
    required: [true, 'محتوى المقال مطلوب']
  },
  image: {
    type: String,
    trim: true
  },
  author: {
    type: String,
    required: [true, 'اسم الكاتب مطلوب'],
    trim: true,
    maxlength: [100, 'اسم الكاتب يجب أن يكون أقل من 100 حرف']
  },
  category: {
    type: String,
    required: [true, 'فئة المقال مطلوبة'],
    trim: true
  },
  tags: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  },
  featured: {
    type: Boolean,
    default: false
  },
  views: {
    type: Number,
    default: 0
  },
  likes: {
    type: Number,
    default: 0
  },
  meta: {
    title: {
      type: String,
      trim: true,
      maxlength: [60, 'عنوان SEO يجب أن يكون أقل من 60 حرف']
    },
    description: {
      type: String,
      trim: true,
      maxlength: [160, 'وصف SEO يجب أن يكون أقل من 160 حرف']
    },
    keywords: {
      type: String,
      trim: true
    }
  },
  publishedAt: {
    type: Date
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
});

// فهرسة للبحث والأداء
articleSchema.index({ slug: 1 }, { unique: true });
articleSchema.index({ status: 1 });
articleSchema.index({ category: 1 });
articleSchema.index({ featured: 1 });
articleSchema.index({ created_at: -1 });
articleSchema.index({ publishedAt: -1 });
articleSchema.index({ 
  title: 'text', 
  excerpt: 'text', 
  content: 'text',
  tags: 'text'
}, {
  weights: {
    title: 10,
    excerpt: 5,
    content: 1,
    tags: 3
  }
});

// تحديث تاريخ النشر عند تغيير الحالة إلى منشور
articleSchema.pre('save', function(next) {
  if (this.isModified('status') && this.status === 'published' && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  next();
});

// طرق النموذج
articleSchema.methods.incrementViews = function() {
  return this.updateOne({ $inc: { views: 1 } });
};

articleSchema.methods.incrementLikes = function() {
  return this.updateOne({ $inc: { likes: 1 } });
};

// طرق ثابتة
articleSchema.statics.findPublished = function() {
  return this.find({ status: 'published' }).sort({ publishedAt: -1 });
};

articleSchema.statics.findFeatured = function() {
  return this.find({ status: 'published', featured: true }).sort({ publishedAt: -1 });
};

articleSchema.statics.findByCategory = function(category) {
  return this.find({ status: 'published', category }).sort({ publishedAt: -1 });
};

articleSchema.statics.searchArticles = function(query) {
  return this.find(
    { 
      $text: { $search: query },
      status: 'published'
    },
    { score: { $meta: 'textScore' } }
  ).sort({ score: { $meta: 'textScore' } });
};

articleSchema.statics.getPopular = function(limit = 10) {
  return this.find({ status: 'published' })
    .sort({ views: -1, likes: -1 })
    .limit(limit);
};

articleSchema.statics.getRecent = function(limit = 10) {
  return this.find({ status: 'published' })
    .sort({ publishedAt: -1 })
    .limit(limit);
};

// تصدير النموذج
const Article = mongoose.models.Article || mongoose.model('Article', articleSchema);

export default Article;
