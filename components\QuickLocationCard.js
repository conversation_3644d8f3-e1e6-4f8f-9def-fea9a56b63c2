import { FaMapMarkerAlt, FaDirections, FaPhone } from 'react-icons/fa';

const QuickLocationCard = () => {
  // إحداثيات الموقع
  const coordinates = {
    lat: 30.038704,
    lng: 31.343061,
    address: "القاهرة، مصر"
  };

  // رابط الاتجاهات
  const directionsUrl = `https://www.google.com/maps/dir//${coordinates.lat},${coordinates.lng}/@${coordinates.lat},${coordinates.lng},16z`;

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
      {/* Header */}
      <div className="bg-gradient-to-r from-[#0A2A4E] to-[#1A3A5E] text-white p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-bold mb-2">موقع المكتب</h3>
            <p className="text-gray-200 text-sm">
              مكتب المحاماة للاستشارات القانونية
            </p>
          </div>
          <FaMapMarkerAlt className="text-[#B08D57] text-3xl" />
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="space-y-4">
          {/* Address */}
          <div className="flex items-start">
            <FaMapMarkerAlt className="text-[#B08D57] mt-1 ml-3 flex-shrink-0" />
            <div>
              <p className="font-semibold text-gray-800">العنوان</p>
              <p className="text-gray-600 text-sm">
                القاهرة، جمهورية مصر العربية<br />
                منطقة مركزية ومتميزة
              </p>
            </div>
          </div>

          {/* Coordinates */}
          <div className="bg-gray-50 rounded-lg p-3">
            <p className="text-xs text-gray-500 mb-1">الإحداثيات</p>
            <p className="font-mono text-sm text-gray-700">
              30°02'19.3"N 31°20'35.0"E
            </p>
          </div>

          {/* Contact Info */}
          <div className="flex items-center">
            <FaPhone className="text-[#B08D57] ml-3" />
            <div>
              <p className="font-semibold text-gray-800">هاتف المكتب</p>
              <p className="text-gray-600 text-sm">+20 2 1234 5678</p>
            </div>
          </div>

          {/* Working Hours */}
          <div className="bg-[#B08D57]/10 rounded-lg p-3">
            <p className="font-semibold text-gray-800 mb-1">ساعات العمل</p>
            <p className="text-gray-600 text-sm">
              السبت - الخميس: 9:00 ص - 6:00 م<br />
              الجمعة: مغلق
            </p>
          </div>
        </div>

        {/* Actions */}
        <div className="mt-6 grid grid-cols-2 gap-3">
          <a
            href={directionsUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center bg-[#B08D57] text-white px-4 py-3 rounded-lg hover:bg-[#9A7A4A] transition-colors duration-200 font-medium text-sm"
          >
            <FaDirections className="ml-2" />
            الاتجاهات
          </a>
          
          <a
            href="tel:+20212345678"
            className="flex items-center justify-center bg-[#0A2A4E] text-white px-4 py-3 rounded-lg hover:bg-[#083A5E] transition-colors duration-200 font-medium text-sm"
          >
            <FaPhone className="ml-2" />
            اتصل بنا
          </a>
        </div>

        {/* Link to full contact page */}
        <div className="mt-4 text-center">
          <a
            href="/contact"
            className="text-[#B08D57] hover:text-[#9A7A4A] text-sm font-medium transition-colors duration-200"
          >
            عرض الخريطة التفاعلية ←
          </a>
        </div>
      </div>
    </div>
  );
};

export default QuickLocationCard;
