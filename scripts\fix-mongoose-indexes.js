import { connectToDatabase } from '../lib/mongodb.js';
import mongoose from 'mongoose';

// استيراد جميع النماذج
import User from '../lib/models/User.js';
import Article from '../lib/models/Article.js';
import Service from '../lib/models/Service.js';
import Category from '../lib/models/Category.js';
import Slide from '../lib/models/Slide.js';
import Page from '../lib/models/Page.js';
import ContactMessage from '../lib/models/ContactMessage.js';
import TeamMember from '../lib/models/TeamMember.js';
import ElectronicService from '../lib/models/ElectronicService.js';
import Expertise from '../lib/models/Expertise.js';
import Setting from '../lib/models/Setting.js';

const models = {
  User,
  Article,
  Service,
  Category,
  Slide,
  Page,
  ContactMessage,
  TeamMember,
  ElectronicService,
  Expertise,
  Setting
};

async function fixMongooseIndexes() {
  try {
    console.log('🔧 بدء إصلاح فهارس Mongoose...\n');

    // الاتصال بقاعدة البيانات
    await connectToDatabase();
    console.log('✅ تم الاتصال بقاعدة البيانات\n');

    // حذف جميع الفهارس الموجودة (عدا _id)
    console.log('🗑️ حذف الفهارس الموجودة...');
    for (const [modelName, Model] of Object.entries(models)) {
      try {
        const collection = Model.collection;
        const indexes = await collection.getIndexes();
        
        console.log(`📋 ${modelName}: ${Object.keys(indexes).length} فهرس موجود`);
        
        // حذف جميع الفهارس عدا _id
        for (const indexName of Object.keys(indexes)) {
          if (indexName !== '_id_') {
            try {
              await collection.dropIndex(indexName);
              console.log(`   ❌ تم حذف الفهرس: ${indexName}`);
            } catch (error) {
              if (!error.message.includes('index not found')) {
                console.log(`   ⚠️ خطأ في حذف الفهرس ${indexName}: ${error.message}`);
              }
            }
          }
        }
      } catch (error) {
        console.log(`❌ خطأ في معالجة ${modelName}: ${error.message}`);
      }
    }

    console.log('\n🔄 إعادة إنشاء الفهارس...');
    
    // إعادة إنشاء الفهارس من النماذج
    for (const [modelName, Model] of Object.entries(models)) {
      try {
        console.log(`🔨 إنشاء فهارس ${modelName}...`);
        await Model.createIndexes();
        console.log(`✅ تم إنشاء فهارس ${modelName}`);
      } catch (error) {
        console.log(`❌ خطأ في إنشاء فهارس ${modelName}: ${error.message}`);
      }
    }

    console.log('\n🔍 التحقق من الفهارس الجديدة...');
    for (const [modelName, Model] of Object.entries(models)) {
      try {
        const indexes = await Model.collection.getIndexes();
        const indexCount = Object.keys(indexes).length;
        console.log(`✅ ${modelName}: ${indexCount} فهرس`);
        
        // عرض الفهارس المهمة
        const importantIndexes = Object.keys(indexes).filter(key => 
          key !== '_id_' && !key.startsWith('_')
        );
        
        if (importantIndexes.length > 0) {
          console.log(`   📋 الفهارس: ${importantIndexes.join(', ')}`);
        }
      } catch (error) {
        console.log(`❌ ${modelName}: خطأ في التحقق - ${error.message}`);
      }
    }

    console.log('\n🎉 تم إصلاح فهارس Mongoose بنجاح!');
    console.log('✅ لن تظهر تحذيرات الفهارس المكررة بعد الآن');

  } catch (error) {
    console.error('❌ خطأ في إصلاح الفهارس:', error);
  } finally {
    // إغلاق الاتصال
    await mongoose.disconnect();
    console.log('\n🔌 تم قطع الاتصال من قاعدة البيانات');
    process.exit(0);
  }
}

// تشغيل السكريبت
fixMongooseIndexes().catch(console.error);
