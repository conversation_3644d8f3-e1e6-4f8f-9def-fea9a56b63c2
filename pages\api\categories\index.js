export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    const fs = require('fs');
    const path = require('path');
    const categoriesPath = path.join(process.cwd(), 'data', 'categories.json');

    let categories = [];
    if (fs.existsSync(categoriesPath)) {
      const data = fs.readFileSync(categoriesPath, 'utf8');
      categories = JSON.parse(data);
    }

    // ترتيب التصنيفات حسب الاسم
    categories = categories.sort((a, b) => a.name.localeCompare(b.name, 'ar'));

    return res.status(200).json({
      success: true,
      categories
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب التصنيفات'
    });
  }
}
