import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FaBars, FaTimes, FaPhone, FaEnvelope, FaGlobe } from 'react-icons/fa';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const router = useRouter();

  const navigation = [
    { name: 'الرئيسية', href: '/' },
    { name: 'من نحن', href: '/about' },
    { name: 'خدماتنا', href: '/services' },
    { name: 'الخدمات الإلكترونية', href: '/electronic-services' },
    { name: 'فريق العمل', href: '/team' },
    { name: 'المقالات', href: '/articles' },
    { name: 'اتصل بنا', href: '/contact' },
  ];

  return (
    <header className="header-container">
      {/* Top Bar */}
      <div className="header-top-bar">
        <div className="container mx-auto px-4">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 text-sm">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-6">
              <div className="flex items-center">
                <FaPhone className="ml-2 text-[#B08D57]" />
                <span>+20 2 1234 5678</span>
              </div>
              <div className="flex items-center">
                <FaEnvelope className="ml-2 text-[#B08D57]" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center">
                <svg className="w-4 h-4 ml-2 text-[#B08D57]" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
                <span>القاهرة، مصر</span>
              </div>
            </div>
            <div className="flex items-center">
              <FaGlobe className="ml-2 text-[#B08D57]" />
              <span>English</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Navigation */}
      <nav className="header-nav">
        <div className="container mx-auto px-4">
        <div className="relative flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          {/* Logo */}
          <Link href="/" className="flex items-center justify-center lg:justify-start">
            <div className="logo-container">
              <div className="logo-title">
                مكتب المحاماة
              </div>
              <div className="logo-subtitle">
                للاستشارات القانونية المتخصصة
              </div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center gap-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`nav-link ${
                  router.pathname === item.href ? 'active' : ''
                }`}
              >
                {item.name}
              </Link>
            ))}
            <button className="btn-consultation">
              استشارة مجانية
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden absolute left-4 top-1/2 transform -translate-y-1/2">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-[#0A2A4E] focus:outline-none p-2 rounded-lg hover:bg-gray-100"
            >
              {isMenuOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden mt-6 pb-4 border-t border-gray-200 pt-4">
            <div className="flex flex-col gap-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`nav-link ${
                    router.pathname === item.href ? 'active' : ''
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              <button className="btn-consultation mt-4 w-full">
                استشارة مجانية
              </button>
            </div>
          </div>
        )}
        </div>
      </nav>
    </header>
  );
};

export default Header;
