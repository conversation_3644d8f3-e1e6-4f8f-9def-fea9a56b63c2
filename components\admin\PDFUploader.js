import { useState, useRef } from 'react';
import { FaFilePdf, FaUpload, FaTrash, FaDownload, FaEye, FaTimes } from 'react-icons/fa';

const PDFUploader = ({ onFileUploaded, existingFiles = [] }) => {
  const [uploading, setUploading] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState(existingFiles);
  const [previewFile, setPreviewFile] = useState(null);
  const fileInputRef = useRef(null);

  const handleFileSelect = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (file.type !== 'application/pdf') {
      alert('يُسمح فقط بملفات PDF');
      return;
    }

    // التحقق من حجم الملف (10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('حجم الملف يجب أن يكون أقل من 10 ميجابايت');
      return;
    }

    setUploading(true);

    try {
      const formData = new FormData();
      formData.append('pdf', file);

      const response = await fetch('/api/upload/pdf', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        const newFile = result.file;
        setUploadedFiles(prev => [...prev, newFile]);
        if (onFileUploaded) {
          onFileUploaded(newFile);
        }
      } else {
        alert(result.error || 'خطأ في رفع الملف');
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('خطأ في رفع الملف');
    } finally {
      setUploading(false);
      // إعادة تعيين input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveFile = (index) => {
    if (confirm('هل أنت متأكد من حذف هذا الملف؟')) {
      setUploadedFiles(prev => prev.filter((_, i) => i !== index));
    }
  };

  const handlePreview = (file) => {
    setPreviewFile(file);
  };

  const handleDownload = (file) => {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const insertPDFIntoEditor = (file) => {
    const pdfHTML = `
      <div class="pdf-attachment" style="border: 2px solid #e5e7eb; border-radius: 8px; padding: 16px; margin: 16px 0; background-color: #f9fafb; direction: rtl;">
        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
          <div style="color: #dc2626; font-size: 24px;">📄</div>
          <div>
            <div style="font-weight: bold; color: #374151; margin-bottom: 4px;">${file.name}</div>
            <div style="font-size: 14px; color: #6b7280;">حجم الملف: ${(file.size / 1024 / 1024).toFixed(2)} ميجابايت</div>
          </div>
        </div>
        <div style="display: flex; gap: 8px;">
          <a href="${file.url}" target="_blank" style="background-color: #3b82f6; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; font-size: 14px; display: inline-flex; align-items: center; gap: 6px;">
            👁️ عرض الملف
          </a>
          <a href="${file.url}" download="${file.name}" style="background-color: #10b981; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; font-size: 14px; display: inline-flex; align-items: center; gap: 6px;">
            ⬇️ تحميل الملف
          </a>
        </div>
      </div>
    `;

    // إدراج HTML في المحرر
    document.execCommand('insertHTML', false, pdfHTML);
  };

  return (
    <div className="pdf-uploader">
      {/* منطقة رفع الملفات */}
      <div className="upload-area">
        <input
          ref={fileInputRef}
          type="file"
          accept=".pdf"
          onChange={handleFileSelect}
          className="hidden"
          id="pdf-upload"
        />
        <label
          htmlFor="pdf-upload"
          className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
            uploading
              ? 'border-gray-300 bg-gray-50 cursor-not-allowed'
              : 'border-blue-300 bg-blue-50 hover:bg-blue-100'
          }`}
        >
          {uploading ? (
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
              <p className="text-sm text-gray-600">جاري رفع الملف...</p>
            </div>
          ) : (
            <div className="flex flex-col items-center">
              <FaUpload className="w-8 h-8 text-blue-600 mb-2" />
              <p className="text-sm text-gray-600 text-center">
                اضغط لاختيار ملف PDF
                <br />
                <span className="text-xs text-gray-500">الحد الأقصى: 10 ميجابايت</span>
              </p>
            </div>
          )}
        </label>
      </div>

      {/* قائمة الملفات المرفوعة */}
      {uploadedFiles.length > 0 && (
        <div className="uploaded-files mt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">الملفات المرفوعة:</h4>
          <div className="space-y-2">
            {uploadedFiles.map((file, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
              >
                <div className="flex items-center space-x-3 space-x-reverse">
                  <FaFilePdf className="text-red-600 text-xl" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{file.name}</p>
                    <p className="text-xs text-gray-500">
                      {(file.size / 1024 / 1024).toFixed(2)} ميجابايت
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <button
                    onClick={() => handlePreview(file)}
                    className="p-2 text-blue-600 hover:bg-blue-100 rounded-full transition-colors"
                    title="معاينة"
                  >
                    <FaEye />
                  </button>
                  <button
                    onClick={() => handleDownload(file)}
                    className="p-2 text-green-600 hover:bg-green-100 rounded-full transition-colors"
                    title="تحميل"
                  >
                    <FaDownload />
                  </button>
                  <button
                    onClick={() => insertPDFIntoEditor(file)}
                    className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                    title="إدراج في المقال"
                  >
                    إدراج
                  </button>
                  <button
                    onClick={() => handleRemoveFile(index)}
                    className="p-2 text-red-600 hover:bg-red-100 rounded-full transition-colors"
                    title="حذف"
                  >
                    <FaTrash />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* نافذة المعاينة */}
      {previewFile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-4 max-w-4xl max-h-[90vh] w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">{previewFile.name}</h3>
              <button
                onClick={() => setPreviewFile(null)}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <FaTimes />
              </button>
            </div>
            <div className="h-96 border rounded">
              <iframe
                src={previewFile.url}
                className="w-full h-full rounded"
                title={previewFile.name}
              />
            </div>
            <div className="flex justify-end space-x-2 space-x-reverse mt-4">
              <button
                onClick={() => handleDownload(previewFile)}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors flex items-center space-x-2 space-x-reverse"
              >
                <FaDownload />
                <span>تحميل</span>
              </button>
              <button
                onClick={() => setPreviewFile(null)}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PDFUploader;
