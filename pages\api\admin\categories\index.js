export default async function handler(req, res) {
  switch (req.method) {
    case 'GET':
      return getCategories(req, res);
    case 'POST':
      return createCategory(req, res);
    default:
      return res.status(405).json({
        success: false,
        message: 'Method not allowed'
      });
  }
}

// الحصول على جميع التصنيفات
const getCategories = async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');
    const categoriesPath = path.join(process.cwd(), 'data', 'categories.json');

    let categories = [];
    if (fs.existsSync(categoriesPath)) {
      const data = fs.readFileSync(categoriesPath, 'utf8');
      categories = JSON.parse(data);
    }

    // ترتيب التصنيفات حسب الاسم
    categories = categories.sort((a, b) => a.name.localeCompare(b.name, 'ar'));

    return res.status(200).json({
      success: true,
      categories
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب التصنيفات'
    });
  }
};

// إنشاء تصنيف جديد
const createCategory = async (req, res) => {
  const { name, slug, description, color } = req.body;

  if (!name) {
    return res.status(400).json({
      success: false,
      message: 'اسم التصنيف مطلوب'
    });
  }

  // إنشاء slug تلقائياً إذا لم يتم توفيره
  const finalSlug = slug || name.toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-');

  try {
    const fs = require('fs');
    const path = require('path');
    const categoriesPath = path.join(process.cwd(), 'data', 'categories.json');

    let categories = [];
    if (fs.existsSync(categoriesPath)) {
      const data = fs.readFileSync(categoriesPath, 'utf8');
      categories = JSON.parse(data);
    }

    // التحقق من عدم تكرار الاسم أو الـ slug
    const existingCategory = categories.find(cat => 
      cat.name === name || cat.slug === finalSlug
    );
    
    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: 'اسم التصنيف أو الرابط المختصر موجود مسبقاً'
      });
    }

    // إنشاء ID جديد
    const newId = categories.length > 0 ? Math.max(...categories.map(c => c.id)) + 1 : 1;

    // إنشاء التصنيف الجديد
    const newCategory = {
      id: newId,
      name,
      slug: finalSlug,
      description: description || '',
      color: color || '#3B82F6',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // إضافة التصنيف الجديد
    categories.push(newCategory);

    // حفظ البيانات
    fs.writeFileSync(categoriesPath, JSON.stringify(categories, null, 2));

    return res.status(201).json({
      success: true,
      message: 'تم إنشاء التصنيف بنجاح',
      category: newCategory
    });
  } catch (error) {
    console.error('Error creating category:', error);
    return res.status(500).json({
      success: false,
      message: 'حدث خطأ في إنشاء التصنيف'
    });
  }
};
