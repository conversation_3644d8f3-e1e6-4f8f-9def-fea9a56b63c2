import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import LocalizedLayout from '../../../components/LocalizedLayout';
import { useTranslation } from '../../../hooks/useTranslation';
import { isValidLocale, defaultLocale } from '../../../lib/i18n';
import { 
  FaGavel, 
  FaBuilding, 
  FaHandshake, 
  FaLightbulb, 
  FaHome, 
  FaGlobeAmericas,
  FaFileContract,
  FaUsers,
  FaArrowRight,
  FaCheck,
  FaStar,
  FaPhone,
  FaEnvelope,
  FaCalendarAlt,
  FaClock,
  FaDollarSign
} from 'react-icons/fa';

export default function ServiceDetailPage() {
  const router = useRouter();
  const { locale, 'service-slug': serviceSlug } = router.query;
  const { t } = useTranslation();
  const [service, setService] = useState(null);
  const [loading, setLoading] = useState(true);

  // بيانات الخدمات
  const servicesData = {
    'judicial-disputes': {
      icon: FaGavel,
      title: t('services.judicialDisputes'),
      subtitle: 'تمثيل قانوني متميز أمام جميع المحاكم ومراكز التحكيم',
      description: 'نقدم خدمات التقاضي والتمثيل القانوني أمام جميع درجات المحاكم ومراكز التحكيم المحلية والدولية. فريقنا من المحامين المتخصصين يضمن لك أفضل تمثيل قانوني وأعلى معدلات النجاح.',
      features: [
        'التقاضي المدني والتجاري',
        'التحكيم الدولي والمحلي',
        'الوساطة وحل النزاعات',
        'تنفيذ الأحكام والقرارات',
        'الطعون والاستئنافات',
        'القضايا الإدارية',
        'النزاعات التجارية',
        'قضايا الملكية الفكرية'
      ],
      process: [
        {
          step: 1,
          title: 'دراسة القضية',
          description: 'تحليل شامل للوقائع والمستندات القانونية'
        },
        {
          step: 2,
          title: 'وضع الاستراتيجية',
          description: 'تطوير خطة قانونية محكمة لتحقيق أفضل النتائج'
        },
        {
          step: 3,
          title: 'التمثيل القانوني',
          description: 'تمثيل احترافي أمام المحاكم ومراكز التحكيم'
        },
        {
          step: 4,
          title: 'المتابعة والتنفيذ',
          description: 'متابعة تنفيذ الأحكام والقرارات الصادرة'
        }
      ],
      pricing: {
        consultation: 'مجانية',
        hourlyRate: '500 ريال/ساعة',
        caseRate: 'حسب طبيعة القضية'
      },
      duration: '3-12 شهر حسب تعقيد القضية',
      color: 'bg-blue-500'
    },
    'corporate-and-investment': {
      icon: FaBuilding,
      title: t('services.corporateAndInvestment'),
      subtitle: 'خدمات قانونية شاملة للشركات والاستثمارات',
      description: 'نساعد الشركات والمستثمرين في جميع احتياجاتهم القانونية من التأسيس إلى النمو والتوسع. خدماتنا تشمل تأسيس الشركات، صياغة العقود، الاندماج والاستحواذ، والامتثال التنظيمي.',
      features: [
        'تأسيس الشركات بجميع أنواعها',
        'صياغة العقود التجارية',
        'الاندماج والاستحواذ',
        'الامتثال التنظيمي',
        'حوكمة الشركات',
        'الاستثمار الأجنبي',
        'التمويل والاستثمار',
        'الطرح العام للأسهم'
      ],
      process: [
        {
          step: 1,
          title: 'التخطيط الاستراتيجي',
          description: 'وضع خطة شاملة للهيكل القانوني المطلوب'
        },
        {
          step: 2,
          title: 'التأسيس والتسجيل',
          description: 'إجراءات التأسيس والحصول على التراخيص'
        },
        {
          step: 3,
          title: 'الهيكلة القانونية',
          description: 'وضع الأنظمة واللوائح الداخلية'
        },
        {
          step: 4,
          title: 'المتابعة والدعم',
          description: 'دعم مستمر للامتثال والنمو'
        }
      ],
      pricing: {
        consultation: 'مجانية',
        incorporation: 'من 5,000 ريال',
        ongoing: 'باقات شهرية مخصصة'
      },
      duration: '2-8 أسابيع للتأسيس',
      color: 'bg-green-500'
    }
    // يمكن إضافة باقي الخدمات هنا...
  };

  useEffect(() => {
    if (locale && !isValidLocale(locale)) {
      router.replace(`/${defaultLocale}/services`);
      return;
    }

    if (serviceSlug && servicesData[serviceSlug]) {
      setService(servicesData[serviceSlug]);
    } else if (serviceSlug) {
      // إذا لم توجد الخدمة، إعادة توجيه لصفحة الخدمات
      router.replace(`/${locale}/services`);
    }
    setLoading(false);
  }, [locale, serviceSlug, router]);

  if (loading || !service) {
    return (
      <LocalizedLayout locale={locale}>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري التحميل...</p>
          </div>
        </div>
      </LocalizedLayout>
    );
  }

  return (
    <LocalizedLayout 
      locale={locale}
      title={`${service.title} - مكتب المحاماة`}
      description={service.subtitle}
    >
      {/* Hero Section */}
      <section className={`${service.color} text-white py-20`}>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <service.icon className="text-6xl mx-auto mb-6" />
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {service.title}
            </h1>
            <p className="text-xl leading-relaxed">
              {service.subtitle}
            </p>
          </div>
        </div>
      </section>

      {/* Service Overview */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">نظرة عامة</h2>
              <p className="text-gray-600 text-lg leading-relaxed mb-8">
                {service.description}
              </p>

              {/* Features */}
              <h3 className="text-2xl font-bold text-gray-900 mb-6">خدماتنا تشمل:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-12">
                {service.features.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <FaCheck className="text-green-500 ml-3 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>

              {/* Process */}
              <h3 className="text-2xl font-bold text-gray-900 mb-6">كيف نعمل:</h3>
              <div className="space-y-6">
                {service.process.map((step, index) => (
                  <div key={index} className="flex items-start">
                    <div className="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-lg ml-4 flex-shrink-0">
                      {step.step}
                    </div>
                    <div>
                      <h4 className="text-xl font-semibold text-gray-900 mb-2">{step.title}</h4>
                      <p className="text-gray-600">{step.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              {/* Service Info Card */}
              <div className="bg-white rounded-lg shadow-lg p-6 mb-6 border border-gray-200">
                <h3 className="text-xl font-bold text-gray-900 mb-4">معلومات الخدمة</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center">
                    <FaClock className="text-primary-600 ml-3" />
                    <div>
                      <div className="font-medium text-gray-900">المدة المتوقعة</div>
                      <div className="text-gray-600">{service.duration}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <FaDollarSign className="text-primary-600 ml-3" />
                    <div>
                      <div className="font-medium text-gray-900">الاستشارة</div>
                      <div className="text-gray-600">{service.pricing.consultation}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <FaCalendarAlt className="text-primary-600 ml-3" />
                    <div>
                      <div className="font-medium text-gray-900">التوفر</div>
                      <div className="text-gray-600">على مدار الساعة</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact Card */}
              <div className="bg-primary-50 rounded-lg p-6 border border-primary-200">
                <h3 className="text-xl font-bold text-primary-900 mb-4">احصل على استشارة مجانية</h3>
                <p className="text-primary-700 mb-6">
                  تواصل معنا الآن للحصول على استشارة مجانية حول خدمة {service.title}
                </p>
                
                <div className="space-y-3">
                  <Link href={`/${locale}/contact-us`}>
                    <button className="w-full bg-primary-600 text-white py-3 px-4 rounded-lg hover:bg-primary-700 transition-colors duration-200 flex items-center justify-center gap-2">
                      <FaEnvelope />
                      تواصل معنا
                    </button>
                  </Link>
                  
                  <a href="tel:+966112345678" className="w-full bg-white text-primary-600 py-3 px-4 rounded-lg border border-primary-600 hover:bg-primary-50 transition-colors duration-200 flex items-center justify-center gap-2">
                    <FaPhone />
                    اتصل بنا الآن
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Related Services */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">خدمات ذات صلة</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {Object.entries(servicesData)
              .filter(([key]) => key !== serviceSlug)
              .slice(0, 3)
              .map(([key, relatedService]) => (
                <div key={key} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                  <div className={`${relatedService.color} p-6 text-white`}>
                    <relatedService.icon className="text-3xl mb-3" />
                    <h3 className="text-lg font-bold">{relatedService.title}</h3>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-600 mb-4">{relatedService.subtitle}</p>
                    <Link href={`/${locale}/services/${key}`}>
                      <button className="text-primary-600 hover:text-primary-700 font-medium flex items-center gap-2">
                        {t('common.readMore')}
                        <FaArrowRight />
                      </button>
                    </Link>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            جاهز للبدء؟
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            احصل على استشارة مجانية الآن وابدأ رحلتك نحو الحل القانوني الأمثل
          </p>
          <Link href={`/${locale}/contact-us`}>
            <button className="btn-accent">
              احجز استشارتك المجانية
            </button>
          </Link>
        </div>
      </section>
    </LocalizedLayout>
  );
}
