import mongoose from 'mongoose';

const contactMessageSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'الاسم مطلوب'],
    trim: true,
    maxlength: [100, 'الاسم يجب أن يكون أقل من 100 حرف']
  },
  email: {
    type: String,
    required: [true, 'البريد الإلكتروني مطلوب'],
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'البريد الإلكتروني غير صحيح']
  },
  phone: {
    type: String,
    trim: true,
    match: [/^[0-9+\-\s()]+$/, 'رقم الهاتف غير صحيح']
  },
  subject: {
    type: String,
    required: [true, 'الموضوع مطلوب'],
    trim: true,
    maxlength: [200, 'الموضوع يجب أن يكون أقل من 200 حرف']
  },
  message: {
    type: String,
    required: [true, 'الرسالة مطلوبة'],
    trim: true,
    maxlength: [2000, 'الرسالة يجب أن تكون أقل من 2000 حرف']
  },
  status: {
    type: String,
    enum: ['unread', 'read', 'replied', 'archived', 'spam'],
    default: 'unread'
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  category: {
    type: String,
    enum: ['general', 'consultation', 'complaint', 'suggestion', 'partnership', 'other'],
    default: 'general'
  },
  source: {
    type: String,
    enum: ['website', 'phone', 'email', 'social', 'referral'],
    default: 'website'
  },
  ipAddress: {
    type: String,
    trim: true
  },
  userAgent: {
    type: String,
    trim: true
  },
  referrer: {
    type: String,
    trim: true
  },
  attachments: [{
    filename: {
      type: String,
      required: true
    },
    originalName: {
      type: String,
      required: true
    },
    mimeType: {
      type: String,
      required: true
    },
    size: {
      type: Number,
      required: true
    },
    path: {
      type: String,
      required: true
    }
  }],
  replies: [{
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    content: {
      type: String,
      required: true,
      trim: true
    },
    sentAt: {
      type: Date,
      default: Date.now
    },
    method: {
      type: String,
      enum: ['email', 'phone', 'internal'],
      default: 'email'
    }
  }],
  tags: [{
    type: String,
    trim: true
  }],
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  readAt: {
    type: Date
  },
  readBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  followUpDate: {
    type: Date
  },
  isFollowUpRequired: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
});

// فهرسة للبحث والأداء
contactMessageSchema.index({ status: 1 });
contactMessageSchema.index({ priority: 1 });
contactMessageSchema.index({ category: 1 });
contactMessageSchema.index({ assignedTo: 1 });
contactMessageSchema.index({ created_at: -1 });
contactMessageSchema.index({ followUpDate: 1 });
contactMessageSchema.index({ email: 1 });
contactMessageSchema.index({ 
  name: 'text', 
  subject: 'text', 
  message: 'text',
  email: 'text'
}, {
  weights: {
    subject: 10,
    name: 5,
    message: 1,
    email: 3
  }
});

// Virtual للتحقق من الحاجة للمتابعة
contactMessageSchema.virtual('needsFollowUp').get(function() {
  if (!this.isFollowUpRequired || !this.followUpDate) return false;
  return this.followUpDate <= new Date();
});

// Virtual لحساب عدد الردود
contactMessageSchema.virtual('repliesCount').get(function() {
  return this.replies ? this.replies.length : 0;
});

// طرق النموذج
contactMessageSchema.methods.markAsRead = function(userId) {
  this.status = 'read';
  this.readAt = new Date();
  this.readBy = userId;
  return this.save();
};

contactMessageSchema.methods.markAsReplied = function() {
  this.status = 'replied';
  return this.save();
};

contactMessageSchema.methods.addReply = function(authorId, content, method = 'email') {
  this.replies.push({
    author: authorId,
    content,
    method,
    sentAt: new Date()
  });
  this.status = 'replied';
  return this.save();
};

contactMessageSchema.methods.assignTo = function(userId) {
  this.assignedTo = userId;
  return this.save();
};

contactMessageSchema.methods.setFollowUp = function(date) {
  this.followUpDate = date;
  this.isFollowUpRequired = true;
  return this.save();
};

contactMessageSchema.methods.addTag = function(tag) {
  if (!this.tags.includes(tag)) {
    this.tags.push(tag);
    return this.save();
  }
  return this;
};

contactMessageSchema.methods.removeTag = function(tag) {
  this.tags = this.tags.filter(t => t !== tag);
  return this.save();
};

// طرق ثابتة
contactMessageSchema.statics.findUnread = function() {
  return this.find({ status: 'unread' }).sort({ created_at: -1 });
};

contactMessageSchema.statics.findByStatus = function(status) {
  return this.find({ status }).sort({ created_at: -1 });
};

contactMessageSchema.statics.findByPriority = function(priority) {
  return this.find({ priority }).sort({ created_at: -1 });
};

contactMessageSchema.statics.findByCategory = function(category) {
  return this.find({ category }).sort({ created_at: -1 });
};

contactMessageSchema.statics.findAssignedTo = function(userId) {
  return this.find({ assignedTo: userId }).sort({ created_at: -1 });
};

contactMessageSchema.statics.findNeedingFollowUp = function() {
  return this.find({
    isFollowUpRequired: true,
    followUpDate: { $lte: new Date() }
  }).sort({ followUpDate: 1 });
};

contactMessageSchema.statics.searchMessages = function(query) {
  return this.find(
    { $text: { $search: query } },
    { score: { $meta: 'textScore' } }
  ).sort({ score: { $meta: 'textScore' } });
};

contactMessageSchema.statics.getStats = async function() {
  const pipeline = [
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ];
  
  const statusStats = await this.aggregate(pipeline);
  const total = await this.countDocuments();
  
  const stats = {
    total,
    unread: 0,
    read: 0,
    replied: 0,
    archived: 0,
    spam: 0
  };
  
  statusStats.forEach(stat => {
    stats[stat._id] = stat.count;
  });
  
  return stats;
};

// تصدير النموذج
const ContactMessage = mongoose.models.ContactMessage || mongoose.model('ContactMessage', contactMessageSchema);

export default ContactMessage;
