/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f7ff',
          100: '#e0efff',
          200: '#b9dfff',
          300: '#7cc4ff',
          400: '#36a5ff',
          500: '#0a84ff',
          600: '#0066cc',
          700: '#0052a3',
          800: '#004785',
          900: '#0a2a4e',
          950: '#061b2f',
        },
        gold: {
          50: '#fefdf8',
          100: '#fdf9e7',
          200: '#faf0c9',
          300: '#f5e2a6',
          400: '#eed082',
          500: '#e5bc65',
          600: '#d4a853',
          700: '#b08d57',
          800: '#8f7147',
          900: '#745d3c',
          950: '#3f311f',
        },
      },
      fontFamily: {
        arabic: ['Cairo', 'Tajawal', 'sans-serif'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
    },
  },
  plugins: [],
}
