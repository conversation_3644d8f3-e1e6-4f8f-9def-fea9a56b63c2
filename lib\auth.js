import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { connectToDatabase } from './mongodb.js';
import { User } from './models/index.js';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// تشفير كلمة المرور
const hashPassword = async (password) => {
  return await bcrypt.hash(password, 12);
};

// التحقق من كلمة المرور
const verifyPassword = async (password, hashedPassword) => {
  return await bcrypt.compare(password, hashedPassword);
};

// إنشاء JWT token
const generateToken = (user) => {
  return jwt.sign(
    { 
      userId: user.id, 
      username: user.username, 
      email: user.email,
      role: user.role 
    },
    JWT_SECRET,
    { expiresIn: '7d' }
  );
};

// التحقق من JWT token
const verifyToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
};

// إنشاء مستخدم جديد
const createUser = async (userData) => {
  const { username, email, password, role = 'admin' } = userData;

  try {
    await connectToDatabase();

    // التحقق من عدم وجود مستخدم بنفس البريد الإلكتروني
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      throw new Error('البريد الإلكتروني مستخدم بالفعل');
    }

    // إنشاء المستخدم الجديد (كلمة المرور ستُشفر تلقائياً في النموذج)
    const newUser = new User({
      username,
      email,
      password,
      role
    });

    const savedUser = await newUser.save();

    return {
      id: savedUser._id.toString(),
      username: savedUser.username,
      email: savedUser.email,
      role: savedUser.role
    };
  } catch (error) {
    throw new Error('فشل في إنشاء المستخدم: ' + error.message);
  }
};

// تسجيل الدخول
const loginUser = async (email, password) => {
  try {
    await connectToDatabase();

    // البحث عن المستخدم بالبريد الإلكتروني
    const user = await User.findByEmail(email);

    if (!user) {
      throw new Error('البريد الإلكتروني غير صحيح');
    }

    // التحقق من كلمة المرور
    const isValidPassword = await user.comparePassword(password);

    if (!isValidPassword) {
      throw new Error('كلمة المرور غير صحيحة');
    }

    // تحديث آخر تسجيل دخول
    await user.updateLastLogin();

    const token = generateToken({
      id: user._id.toString(),
      username: user.username,
      email: user.email,
      role: user.role
    });

    return {
      user: {
        id: user._id.toString(),
        username: user.username,
        email: user.email,
        role: user.role
      },
      token
    };
  } catch (error) {
    throw new Error(error.message);
  }
};

// الحصول على المستخدم من التوكن
const getUserFromToken = async (token) => {
  const decoded = verifyToken(token);
  if (!decoded) return null;

  try {
    await connectToDatabase();
    const user = await User.findById(decoded.userId).select('-password');

    if (!user) return null;

    return {
      id: user._id.toString(),
      username: user.username,
      email: user.email,
      role: user.role
    };
  } catch (error) {
    return null;
  }
};

// إنشاء مستخدم افتراضي إذا لم يكن موجوداً
const createDefaultUser = async () => {
  try {
    await connectToDatabase();

    const userCount = await User.countDocuments();

    if (userCount === 0) {
      await createUser({
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin'
      });
      console.log('تم إنشاء المستخدم الافتراضي: <EMAIL> / admin123');
    }
  } catch (error) {
    console.error('خطأ في إنشاء المستخدم الافتراضي:', error);
  }
};

// تشغيل إنشاء المستخدم الافتراضي
createDefaultUser();

export {
  hashPassword,
  verifyPassword,
  generateToken,
  verifyToken,
  createUser,
  loginUser,
  getUserFromToken
};
