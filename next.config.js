﻿/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,

  // Internationalization configuration
  i18n: {
    locales: ['ar', 'en'],
    defaultLocale: 'ar',
    localeDetection: false, // نعطل الكشف التلقائي لأننا نستخدم middleware مخصص
  },

  // Development configuration
  ...(process.env.NODE_ENV === 'development' && {
    onDemandEntries: {
      // Period (in ms) where the server will keep pages in the buffer
      maxInactiveAge: 25 * 1000,
      // Number of pages that should be kept simultaneously without being disposed
      pagesBufferLength: 2,
    }
  }),

  // Compiler options for SWC
  compiler: {
    // Remove console.log in production
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Webpack configuration to handle ES modules and HMR
  webpack: (config, { dev, isServer }) => {
    if (isServer) {
      config.externals = [...config.externals, 'bcryptjs', 'jsonwebtoken'];
    }

    // تحسين HMR في التطوير
    if (dev && !isServer) {
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
      };
    }

    return config;
  },

  // Image optimization
  images: {
    domains: ['images.unsplash.com', 'via.placeholder.com'],
    unoptimized: true
  },

  // Environment variables
  env: {
    JWT_SECRET: process.env.JWT_SECRET || 'your-secret-key-change-in-production',
    MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/law_firm_db',
  },

  // Redirects for old URLs
  async redirects() {
    return [
      {
        source: '/',
        destination: '/ar',
        permanent: true,
      },
      {
        source: '/home',
        destination: '/ar',
        permanent: true,
      },
      {
        source: '/about',
        destination: '/ar/about-us',
        permanent: true,
      },
      {
        source: '/services',
        destination: '/ar/services',
        permanent: true,
      },
      {
        source: '/team',
        destination: '/ar/our-team',
        permanent: true,
      },
      {
        source: '/contact',
        destination: '/ar/contact-us',
        permanent: true,
      },
      {
        source: '/articles',
        destination: '/ar/blog',
        permanent: true,
      }
    ];
  }
};

export default nextConfig;
