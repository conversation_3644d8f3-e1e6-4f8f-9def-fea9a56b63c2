import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '../../contexts/AuthContext';
import {
  FaBars,
  FaTimes,
  FaTachometerAlt,
  FaImages,
  FaNewspaper,
  FaCog,
  FaUsers,
  FaConciergeBell,
  FaSignOutAlt,
  FaUser,
  FaEnvelope,
  FaFileAlt,
  FaGavel,
  FaLaptop,
  FaTags,
  FaInfoCircle
} from 'react-icons/fa';

const AdminLayout = ({ children, title = 'لوحة التحكم' }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, loading, isAuthenticated, logout } = useAuth();
  const router = useRouter();

  // التحقق من المصادقة باستخدام AuthContext
  useEffect(() => {
    if (!loading && !isAuthenticated && router.pathname !== '/admin/login') {
      //window.location.href = '/admin/login';
    }
  }, [loading, isAuthenticated, router]);

  const handleLogout = async () => {
    if (!confirm('هل أنت متأكد من تسجيل الخروج؟')) {
      return;
    }

    await logout();
  };

  const navigation = [
    {
      name: 'لوحة التحكم',
      href: '/admin',
      icon: FaTachometerAlt,
      current: router.pathname === '/admin' || router.pathname === '/admin/dashboard'
    },
    {
      name: 'إدارة الشرائح',
      href: '/admin/slides',
      icon: FaImages,
      current: router.pathname.startsWith('/admin/slides')
    },
    {
      name: 'إدارة المقالات',
      href: '/admin/articles',
      icon: FaNewspaper,
      current: router.pathname.startsWith('/admin/articles')
    },
    {
      name: 'إدارة الخدمات',
      href: '/admin/services',
      icon: FaConciergeBell,
      current: router.pathname.startsWith('/admin/services')
    },
    {
      name: 'الخدمات الإلكترونية',
      href: '/admin/electronic-services',
      icon: FaLaptop,
      current: router.pathname.startsWith('/admin/electronic-services')
    },
    {
      name: 'إدارة التصنيفات',
      href: '/admin/categories',
      icon: FaTags,
      current: router.pathname.startsWith('/admin/categories')
    },
    {
      name: 'إدارة الصفحات',
      href: '/admin/pages',
      icon: FaFileAlt,
      current: router.pathname.startsWith('/admin/pages')
    },
    {
      name: 'مجالات الخبرة القانونية',
      href: '/admin/expertise',
      icon: FaGavel,
      current: router.pathname.startsWith('/admin/expertise')
    },
    {
      name: 'إدارة الفريق',
      href: '/admin/team',
      icon: FaUsers,
      current: router.pathname.startsWith('/admin/team')
    },
    {
      name: 'من نحن',
      href: '/admin/about-us',
      icon: FaInfoCircle,
      current: router.pathname.startsWith('/admin/about-us')
    },
    {
      name: 'رسائل التواصل',
      href: '/admin/messages',
      icon: FaEnvelope,
      current: router.pathname.startsWith('/admin/messages')
    },
    {
      name: 'الإعدادات',
      href: '/admin/settings',
      icon: FaCog,
      current: router.pathname.startsWith('/admin/settings')
    }
  ];

  // عرض شاشة التحميل أثناء التحقق من المصادقة
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-primary-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-primary-700 font-medium">جاري التحقق من المصادقة...</p>
        </div>
      </div>
    );
  }

  // إذا لم يكن مصادق عليه، لا تعرض شيئاً (سيتم التوجيه إلى صفحة تسجيل الدخول)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>{title} - لوحة التحكم الإدارية</title>
        <meta name="robots" content="noindex, nofollow" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="admin-layout admin-content-wrapper min-h-screen bg-gray-50 flex lg:flex-row">
        {/* Sidebar */}
        <div className={`fixed inset-y-0 right-0 z-50 w-64 bg-gradient-to-b from-primary-900 to-primary-950 shadow-2xl transform ${
          sidebarOpen ? 'translate-x-0' : 'translate-x-full'
        } transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-auto lg:w-64 lg:flex lg:flex-col lg:h-screen`}>

          {/* Sidebar Header */}
          <div className="flex items-center justify-between h-16 px-4 bg-primary-950/50 backdrop-blur-sm border-b border-primary-800/50">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-r from-accent-400 to-accent-500 rounded-lg flex items-center justify-center ml-3">
                <FaGavel className="text-white text-sm" />
              </div>
              <h1 className="text-white font-bold text-lg">لوحة التحكم</h1>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden text-white hover:text-gray-300 transition-colors"
            >
              <FaTimes size={20} />
            </button>
          </div>

          {/* User Info */}
          <div className="p-4 border-b border-primary-800/50">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-r from-accent-400 to-accent-500 rounded-full flex items-center justify-center shadow-lg">
                <FaUser className="text-white text-lg" />
              </div>
              <div className="mr-3">
                <p className="text-white font-semibold">{user?.username || 'المدير'}</p>
                <p className="text-primary-300 text-sm flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full ml-2"></span>
                  {user?.role === 'admin' ? 'مدير النظام' : 'مستخدم'}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="mt-6 px-3">
            {navigation.map((item) => (
              <Link key={item.name} href={item.href}>
                <div className={`group flex items-center px-4 py-3 text-sm font-medium rounded-xl mb-2 transition-all duration-200 ${
                  item.current
                    ? 'bg-gradient-to-r from-accent-500 to-accent-600 text-white shadow-lg transform scale-105'
                    : 'text-primary-200 hover:bg-primary-800/50 hover:text-white hover:translate-x-1'
                }`}>
                  <item.icon className={`ml-3 h-5 w-5 ${item.current ? 'text-white' : 'text-primary-300'}`} />
                  {item.name}
                  {item.name === 'رسائل التواصل' && (
                    <span className="mr-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                      5
                    </span>
                  )}
                  {item.current && (
                    <div className="mr-auto w-2 h-2 bg-white rounded-full"></div>
                  )}
                </div>
              </Link>
            ))}
          </nav>

          {/* Logout Button */}
          <div className="absolute bottom-4 left-3 right-3">
            <button
              onClick={handleLogout}
              className="w-full flex items-center px-4 py-3 text-sm font-medium text-primary-200 hover:bg-red-600 hover:text-white rounded-xl transition-all duration-200 border border-primary-800 hover:border-red-500 group"
            >
              <FaSignOutAlt className="ml-3 h-5 w-5 group-hover:rotate-12 transition-transform" />
              تسجيل الخروج
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 lg:flex-1 lg:overflow-hidden">
          {/* Top Bar */}
          <div className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
            <div className="flex items-center justify-between h-16 px-4 lg:px-6">
              <div className="flex items-center">
                <button
                  onClick={() => setSidebarOpen(true)}
                  className="lg:hidden text-gray-600 hover:text-gray-900 p-2 rounded-lg hover:bg-gray-100 transition-colors ml-2"
                >
                  <FaBars size={20} />
                </button>

                <h2 className="text-xl lg:text-2xl font-bold text-gray-900">{title}</h2>
              </div>

              <div className="flex items-center">
                <Link href="/" target="_blank">
                  <button className="bg-primary-600 text-white px-3 py-2 lg:px-4 lg:py-2 rounded-lg hover:bg-primary-700 transition-colors text-sm font-medium shadow-md">
                    عرض الموقع
                  </button>
                </Link>
              </div>
            </div>
          </div>

          {/* Page Content */}
          <main className="p-4 lg:p-6">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </main>
        </div>

        {/* Sidebar Overlay */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </div>
    </>
  );
};

export default AdminLayout;
