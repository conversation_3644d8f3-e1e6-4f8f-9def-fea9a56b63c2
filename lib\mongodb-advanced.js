import { connectToDatabase } from './mongodb.js';
import {
  User,
  Article,
  Service,
  Category,
  Slide,
  Page,
  ContactMessage,
  TeamMember,
  ElectronicService,
  Expertise,
  Setting
} from './models/index.js';

// خريطة النماذج
const modelMap = {
  articles: Article,
  services: Service,
  expertise: Expertise,
  categories: Category,
  'service-categories': Category,
  slides: Slide,
  pages: Page,
  team_members: TeamMember,
  contact_messages: ContactMessage,
  electronic_services: ElectronicService,
  users: User,
  settings: Setting
};

// إعدادات التخزين المؤقت في الذاكرة
const cache = new Map();
const cacheConfig = {
  enabled: true,
  ttl: 5 * 60 * 1000, // 5 دقائق
  maxSize: 100
};

// دالة للحصول على النموذج
function getModel(table) {
  const model = modelMap[table];
  if (!model) {
    throw new Error(`Model for table '${table}' not found`);
  }
  return model;
}

// دالة للتخزين المؤقت
function getCacheKey(operation, table, params = {}) {
  return `${operation}_${table}_${JSON.stringify(params)}`;
}

function setCache(key, value) {
  if (!cacheConfig.enabled) return;
  
  // إزالة العناصر القديمة إذا تجاوز الحد الأقصى
  if (cache.size >= cacheConfig.maxSize) {
    const firstKey = cache.keys().next().value;
    cache.delete(firstKey);
  }
  
  cache.set(key, {
    value,
    timestamp: Date.now()
  });
}

function getCache(key) {
  if (!cacheConfig.enabled) return null;
  
  const cached = cache.get(key);
  if (!cached) return null;
  
  // التحقق من انتهاء صلاحية التخزين المؤقت
  if (Date.now() - cached.timestamp > cacheConfig.ttl) {
    cache.delete(key);
    return null;
  }
  
  return cached.value;
}

function clearCache(pattern = null) {
  if (pattern) {
    for (const key of cache.keys()) {
      if (key.includes(pattern)) {
        cache.delete(key);
      }
    }
  } else {
    cache.clear();
  }
}

// دالة للبحث المتقدم
export async function advancedSearch(table, options = {}) {
  try {
    await connectToDatabase();
    
    const {
      searchTerm = '',
      fields = [],
      filters = {},
      sortBy = 'created_at',
      sortOrder = 'desc',
      page = 1,
      limit = 10,
      useCache = true
    } = options;
    
    const cacheKey = getCacheKey('search', table, options);
    
    // التحقق من التخزين المؤقت
    if (useCache) {
      const cachedResult = getCache(cacheKey);
      if (cachedResult) {
        return cachedResult;
      }
    }
    
    const Model = getModel(table);
    let query = {};
    
    // إضافة البحث النصي
    if (searchTerm) {
      if (fields.length > 0) {
        // البحث في حقول محددة
        const searchConditions = fields.map(field => ({
          [field]: { $regex: searchTerm, $options: 'i' }
        }));
        query.$or = searchConditions;
      } else {
        // البحث النصي الكامل
        query.$text = { $search: searchTerm };
      }
    }
    
    // إضافة المرشحات
    Object.assign(query, filters);
    
    // إعداد الترتيب
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    
    // حساب التخطي للصفحات
    const skip = (page - 1) * limit;
    
    // تنفيذ الاستعلام
    const [results, total] = await Promise.all([
      Model.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Model.countDocuments(query)
    ]);
    
    const searchResult = {
      results: results.map(item => ({
        ...item,
        id: item._id.toString(),
        _id: undefined
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
    
    // حفظ في التخزين المؤقت
    if (useCache) {
      setCache(cacheKey, searchResult);
    }
    
    return searchResult;
  } catch (error) {
    console.error('Advanced search error:', error);
    throw error;
  }
}

// دالة لإنشاء نسخة احتياطية
export async function createBackup(table = null) {
  try {
    await connectToDatabase();
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    if (table) {
      // نسخ احتياطي لجدول واحد
      const Model = getModel(table);
      const data = await Model.find({}).lean();
      
      const backupData = {
        table,
        timestamp,
        count: data.length,
        data: data.map(item => ({
          ...item,
          id: item._id.toString(),
          _id: undefined
        }))
      };
      
      return backupData;
    } else {
      // نسخ احتياطي لجميع الجداول
      const tables = Object.keys(modelMap);
      const backupData = {
        timestamp,
        tables: {}
      };
      
      for (const tableName of tables) {
        try {
          const Model = getModel(tableName);
          const data = await Model.find({}).lean();
          
          backupData.tables[tableName] = {
            count: data.length,
            data: data.map(item => ({
              ...item,
              id: item._id.toString(),
              _id: undefined
            }))
          };
        } catch (error) {
          console.error(`Error backing up ${tableName}:`, error);
          backupData.tables[tableName] = {
            error: error.message,
            count: 0,
            data: []
          };
        }
      }
      
      return backupData;
    }
  } catch (error) {
    console.error('Backup creation error:', error);
    throw error;
  }
}

// دالة لاستعادة النسخة الاحتياطية
export async function restoreBackup(backupData) {
  try {
    await connectToDatabase();
    
    if (backupData.table) {
      // استعادة جدول واحد
      const Model = getModel(backupData.table);
      
      // حذف البيانات الموجودة
      await Model.deleteMany({});
      
      // إدراج البيانات المستعادة
      if (backupData.data && backupData.data.length > 0) {
        const processedData = backupData.data.map(item => {
          const { id, ...rest } = item;
          return rest;
        });
        
        await Model.insertMany(processedData);
      }
      
      return { success: true, restored: backupData.data.length };
    } else {
      // استعادة جميع الجداول
      const results = {};
      
      for (const [tableName, tableData] of Object.entries(backupData.tables)) {
        try {
          if (tableData.error) {
            results[tableName] = { success: false, error: tableData.error };
            continue;
          }
          
          const Model = getModel(tableName);
          
          // حذف البيانات الموجودة
          await Model.deleteMany({});
          
          // إدراج البيانات المستعادة
          if (tableData.data && tableData.data.length > 0) {
            const processedData = tableData.data.map(item => {
              const { id, ...rest } = item;
              return rest;
            });
            
            await Model.insertMany(processedData);
          }
          
          results[tableName] = { success: true, restored: tableData.data.length };
        } catch (error) {
          console.error(`Error restoring ${tableName}:`, error);
          results[tableName] = { success: false, error: error.message };
        }
      }
      
      return results;
    }
  } catch (error) {
    console.error('Backup restoration error:', error);
    throw error;
  }
}

// دالة لإحصائيات قاعدة البيانات
export async function getDatabaseStats() {
  try {
    await connectToDatabase();
    
    const tables = Object.keys(modelMap);
    const stats = {};
    
    for (const table of tables) {
      try {
        const Model = getModel(table);
        const count = await Model.countDocuments();
        
        // حساب حجم المجموعة (تقريبي)
        const sampleDoc = await Model.findOne().lean();
        const estimatedSize = sampleDoc ? 
          JSON.stringify(sampleDoc).length * count : 0;
        
        stats[table] = {
          count,
          estimatedSize,
          lastModified: new Date().toISOString()
        };
      } catch (error) {
        console.error(`Error getting stats for ${table}:`, error);
        stats[table] = {
          count: 0,
          estimatedSize: 0,
          error: error.message
        };
      }
    }
    
    return stats;
  } catch (error) {
    console.error('Database stats error:', error);
    throw error;
  }
}

// دالة لتصدير البيانات
export async function exportData(format = 'json', tables = null) {
  try {
    await connectToDatabase();
    
    const tablesToExport = tables || Object.keys(modelMap);
    const exportData = {};
    
    for (const table of tablesToExport) {
      try {
        const Model = getModel(table);
        const data = await Model.find({}).lean();
        
        exportData[table] = data.map(item => ({
          ...item,
          id: item._id.toString(),
          _id: undefined
        }));
      } catch (error) {
        console.error(`Error exporting ${table}:`, error);
        exportData[table] = [];
      }
    }
    
    if (format === 'json') {
      return JSON.stringify(exportData, null, 2);
    }
    
    return exportData;
  } catch (error) {
    console.error('Export error:', error);
    return null;
  }
}

// دالة لتحسين الأداء
export async function optimizeDatabase() {
  try {
    await connectToDatabase();
    
    const results = {};
    
    // إنشاء الفهارس
    for (const [tableName, Model] of Object.entries(modelMap)) {
      try {
        await Model.createIndexes();
        results[tableName] = { indexes: 'created' };
      } catch (error) {
        results[tableName] = { error: error.message };
      }
    }
    
    // تنظيف التخزين المؤقت
    clearCache();
    results.cache = 'cleared';
    
    return results;
  } catch (error) {
    console.error('Database optimization error:', error);
    throw error;
  }
}

// تصدير جميع الدوال
export default {
  advancedSearch,
  createBackup,
  restoreBackup,
  getDatabaseStats,
  exportData,
  optimizeDatabase,
  cache: {
    get: getCache,
    set: setCache,
    clear: clearCache,
    config: cacheConfig
  }
};
