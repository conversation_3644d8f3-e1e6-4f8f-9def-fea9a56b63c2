import Link from 'next/link';
import { FaArrowLeft } from 'react-icons/fa';

function ServiceCard({ icon: Icon, title, description, features, link }) {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6 card-hover group">
      {/* Icon */}
      <div className="flex justify-center mb-4">
        <div className="w-16 h-16 bg-gradient-to-br from-primary-900 to-primary-700 rounded-full flex items-center justify-center group-hover:from-gold-600 group-hover:to-gold-500 transition-all duration-300">
          <Icon className="text-white text-2xl" />
        </div>
      </div>

      {/* Title */}
      <h3 className="text-xl font-bold text-primary-900 text-center mb-3 group-hover:text-gold-600 transition-colors duration-300">
        {title}
      </h3>

      {/* Description */}
      <p className="text-gray-600 text-center mb-4 leading-relaxed">
        {description}
      </p>

      {/* Features */}
      {features && features.length > 0 && (
        <ul className="space-y-2 mb-6">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center text-sm text-gray-700">
              <div className="w-2 h-2 bg-gold-500 rounded-full ml-3 flex-shrink-0"></div>
              {feature}
            </li>
          ))}
        </ul>
      )}

      {/* CTA Button */}
      <div className="text-center">
        <Link href={link || '/services'}>
          <button className="inline-flex items-center text-primary-900 hover:text-gold-600 font-semibold transition-colors duration-200 group">
            اعرف المزيد
            <FaArrowLeft className="mr-2 group-hover:translate-x-1 transition-transform duration-200" />
          </button>
        </Link>
      </div>
    </div>
  );
}

export default ServiceCard;
