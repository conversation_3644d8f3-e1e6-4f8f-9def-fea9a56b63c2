// استيراد طبقة قاعدة البيانات الجديدة (MongoDB)
import mongoDatabase from './database-mongodb.js';

const {
  readData: mongoReadData,
  writeData: mongoWriteData,
  generateId: mongoGenerateId,
  initDatabase: mongoInitDatabase,
  db: mongoDb
} = mongoDatabase;

// استخدام دوال MongoDB مع الحفاظ على نفس الواجهة
const readData = async (table) => {
  return await mongoReadData(table);
};

const writeData = async (table, data) => {
  return await mongoWriteData(table, data);
};

const generateId = () => {
  return mongoGenerateId();
};

const initDatabase = async () => {
  return await mongoInitDatabase();
};

// استخدام طبقة MongoDB مع الحفاظ على نفس الواجهة
const db = mongoDb;

// تهيئة قاعدة البيانات
initDatabase();

export default db;
export { readData, writeData, generateId };
