import { useState, useEffect } from 'react';
import Link from 'next/link';
import { FaArrowLeft, FaChevronLeft, FaChevronRight } from 'react-icons/fa';

function Hero() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [slides, setSlides] = useState([]);
  const [loading, setLoading] = useState(true);

  // جلب الشرائح من قاعدة البيانات
  useEffect(() => {
    const fetchSlides = async () => {
      try {
        const response = await fetch('/api/slides');
        const data = await response.json();

        if (data.success && data.slides.length > 0) {
          setSlides(data.slides);
        } else {
          // استخدام شريحة افتراضية في حالة عدم وجود شرائح
          setSlides([{
            id: 'default',
            title: 'خبرة قانونية يمكنك الاعتماد عليها',
            subtitle: 'نحن شركاؤك نحو الحلول القانونية المتميزة',
            description: 'مكتب محاماة متخصص في تقديم الاستشارات القانونية والخدمات القانونية المتكاملة للأفراد والشركات بأعلى معايير الجودة والاحترافية',
            image: 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
            cta: 'اطلب استشارة الآن',
            cta_link: '/contact'
          }]);
        }
      } catch (error) {
        console.error('Error fetching slides:', error);
        // استخدام شريحة افتراضية في حالة الخطأ
        setSlides([{
          id: 'default',
          title: 'خبرة قانونية يمكنك الاعتماد عليها',
          subtitle: 'نحن شركاؤك نحو الحلول القانونية المتميزة',
          description: 'مكتب محاماة متخصص في تقديم الاستشارات القانونية والخدمات القانونية المتكاملة للأفراد والشركات بأعلى معايير الجودة والاحترافية',
          image: 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
          cta: 'اطلب استشارة الآن',
          cta_link: '/contact'
        }]);
      } finally {
        setLoading(false);
      }
    };

    fetchSlides();
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(timer);
  }, [slides.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  // عرض شاشة تحميل
  if (loading) {
    return (
      <section className="relative h-screen overflow-hidden bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold-400 mx-auto mb-4"></div>
          <p className="text-white text-lg">جاري تحميل الشرائح...</p>
        </div>
      </section>
    );
  }

  // إذا لم توجد شرائح
  if (!slides.length) {
    return null;
  }

  return (
    <section className="relative h-screen overflow-hidden">
      {/* Slides */}
      {slides.map((slide, index) => (
        <div
          key={slide.id}
          className={`absolute inset-0 transition-opacity duration-1000 ${
            index === currentSlide ? 'opacity-100' : 'opacity-0'
          }`}
        >
          {/* Background Image */}
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{ backgroundImage: `url(${slide.image})` }}
          >
            <div className="absolute inset-0 bg-black bg-opacity-50"></div>
          </div>

          {/* Content */}
          <div className="relative z-10 h-full flex items-center">
            <div className="container mx-auto px-4">
              <div className="max-w-3xl">
                <h1 className="text-4xl md:text-6xl font-bold text-white mb-4 fade-in">
                  {slide.title}
                </h1>
                <h2 className="text-xl md:text-2xl text-gold-400 mb-6 fade-in">
                  {slide.subtitle}
                </h2>
                <p className="text-lg text-gray-200 mb-8 leading-relaxed fade-in">
                  {slide.description}
                </p>
                <div className="flex flex-col sm:flex-row gap-4 fade-in">
                  {slide.cta && slide.cta_link && (
                    <Link href={slide.cta_link}>
                      <button className={`${slide.cta_style === 'secondary' ? 'btn-secondary' : 'btn-primary'} flex items-center justify-center`}>
                        {slide.cta}
                        <FaArrowLeft className="mr-2" />
                      </button>
                    </Link>
                  )}
                  <Link href="/contact">
                    <button className="btn-secondary">
                      اتصل بنا
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}

      {/* Navigation Arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full transition-all duration-200"
      >
        <FaChevronLeft size={20} />
      </button>
      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full transition-all duration-200"
      >
        <FaChevronRight size={20} />
      </button>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 flex space-x-2 space-x-reverse">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-200 ${
              index === currentSlide ? 'bg-gold-400' : 'bg-white bg-opacity-50'
            }`}
          />
        ))}
      </div>

      {/* Scroll Down Indicator */}
      <div className="absolute bottom-8 right-8 z-20 animate-bounce">
        <div className="text-white text-center">
          <div className="w-6 h-10 border-2 border-white rounded-full mx-auto mb-2">
            <div className="w-1 h-3 bg-white rounded-full mx-auto mt-2 animate-pulse"></div>
          </div>
          <span className="text-sm">اكتشف المزيد</span>
        </div>
      </div>
    </section>
  );
}

export default Hero;
