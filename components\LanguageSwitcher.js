import { useRouter } from 'next/router';
import Link from 'next/link';
import { FaGlobe } from 'react-icons/fa';
import { getAlternateLocale, getLocalizedPath, getTranslation } from '../lib/i18n';

const LanguageSwitcher = ({ currentLocale = 'ar' }) => {
  const router = useRouter();
  const alternateLocale = getAlternateLocale(currentLocale);
  const alternatePath = getLocalizedPath(router.asPath, alternateLocale);

  const languages = {
    ar: { name: 'العربية', flag: '🇸🇦' },
    en: { name: 'English', flag: '🇺🇸' }
  };
  return (
    <div className="relative group">
      <button className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary-600 transition-colors">
        <FaGlobe className="text-primary-600" />
        <span>{languages['en'].flag}</span>
        <span className="hidden sm:inline">{languages['en'].name}</span>
      </button>
      
      <div className="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
        <div className="py-2">
          <Link href={alternatePath}>
            <div className="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors cursor-pointer">
              <span className="text-lg">{languages[alternateLocale].flag}</span>
              <span>{languages[alternateLocale].name}</span>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default LanguageSwitcher;
