import mongoose from 'mongoose';

const slideSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'عنوان الشريحة مطلوب'],
    trim: true,
    maxlength: [200, 'العنوان يجب أن يكون أقل من 200 حرف']
  },
  subtitle: {
    type: String,
    trim: true,
    maxlength: [300, 'العنوان الفرعي يجب أن يكون أقل من 300 حرف']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, 'الوصف يجب أن يكون أقل من 1000 حرف']
  },
  image: {
    type: String,
    required: [true, 'صورة الشريحة مطلوبة'],
    trim: true
  },
  mobileImage: {
    type: String,
    trim: true
  },
  video: {
    type: String,
    trim: true
  },
  cta_text: {
    type: String,
    trim: true,
    maxlength: [50, 'نص الزر يجب أن يكون أقل من 50 حرف']
  },
  cta_link: {
    type: String,
    trim: true
  },
  cta_style: {
    type: String,
    enum: ['primary', 'secondary', 'outline', 'ghost'],
    default: 'primary'
  },
  order_index: {
    type: Number,
    required: true,
    default: 0
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'scheduled'],
    default: 'active'
  },
  startDate: {
    type: Date
  },
  endDate: {
    type: Date
  },
  animation: {
    type: {
      type: String,
      enum: ['fade', 'slide', 'zoom', 'none'],
      default: 'fade'
    },
    duration: {
      type: Number,
      default: 5000,
      min: 1000,
      max: 30000
    },
    direction: {
      type: String,
      enum: ['left', 'right', 'up', 'down'],
      default: 'left'
    }
  },
  overlay: {
    enabled: {
      type: Boolean,
      default: true
    },
    color: {
      type: String,
      default: 'rgba(0, 0, 0, 0.4)'
    },
    opacity: {
      type: Number,
      default: 0.4,
      min: 0,
      max: 1
    }
  },
  textPosition: {
    type: String,
    enum: ['left', 'center', 'right'],
    default: 'left'
  },
  textColor: {
    type: String,
    default: '#ffffff'
  },
  views: {
    type: Number,
    default: 0
  },
  clicks: {
    type: Number,
    default: 0
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
});

// فهرسة للبحث والأداء
slideSchema.index({ order_index: 1 });
slideSchema.index({ status: 1 });
slideSchema.index({ startDate: 1, endDate: 1 });
slideSchema.index({ created_at: -1 });

// Virtual للتحقق من صحة الجدولة
slideSchema.virtual('isScheduled').get(function() {
  if (this.status !== 'scheduled') return false;
  
  const now = new Date();
  const start = this.startDate;
  const end = this.endDate;
  
  if (start && start > now) return false;
  if (end && end < now) return false;
  
  return true;
});

// Virtual للتحقق من انتهاء الصلاحية
slideSchema.virtual('isExpired').get(function() {
  if (!this.endDate) return false;
  return this.endDate < new Date();
});

// طرق النموذج
slideSchema.methods.incrementViews = function() {
  return this.updateOne({ $inc: { views: 1 } });
};

slideSchema.methods.incrementClicks = function() {
  return this.updateOne({ $inc: { clicks: 1 } });
};

slideSchema.methods.activate = function() {
  return this.updateOne({ status: 'active' });
};

slideSchema.methods.deactivate = function() {
  return this.updateOne({ status: 'inactive' });
};

// طرق ثابتة
slideSchema.statics.findActive = function() {
  return this.find({ 
    status: { $in: ['active', 'scheduled'] }
  }).sort({ order_index: 1 });
};

slideSchema.statics.findVisible = function() {
  const now = new Date();
  return this.find({
    $or: [
      { status: 'active' },
      {
        status: 'scheduled',
        $or: [
          { startDate: { $lte: now }, endDate: { $gte: now } },
          { startDate: { $lte: now }, endDate: null },
          { startDate: null, endDate: { $gte: now } }
        ]
      }
    ]
  }).sort({ order_index: 1 });
};

slideSchema.statics.reorderSlides = async function(slideIds) {
  const updates = slideIds.map((id, index) => ({
    updateOne: {
      filter: { _id: id },
      update: { order_index: index }
    }
  }));
  
  return this.bulkWrite(updates);
};

slideSchema.statics.getStats = async function() {
  const pipeline = [
    {
      $group: {
        _id: null,
        totalSlides: { $sum: 1 },
        activeSlides: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        totalViews: { $sum: '$views' },
        totalClicks: { $sum: '$clicks' }
      }
    }
  ];
  
  const result = await this.aggregate(pipeline);
  return result[0] || {
    totalSlides: 0,
    activeSlides: 0,
    totalViews: 0,
    totalClicks: 0
  };
};

// Middleware لتنظيف الشرائح المنتهية الصلاحية
slideSchema.statics.cleanExpiredSlides = async function() {
  const now = new Date();
  return this.updateMany(
    { 
      status: 'scheduled',
      endDate: { $lt: now }
    },
    { status: 'inactive' }
  );
};

// تصدير النموذج
const Slide = mongoose.models.Slide || mongoose.model('Slide', slideSchema);

export default Slide;
