import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FaBars, FaTimes, FaPhone, FaEnvelope, FaChevronDown } from 'react-icons/fa';
import LanguageSwitcher from './LanguageSwitcher';
import { useTranslation } from '../hooks/useTranslation';

const LocalizedHeader = ({ locale }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const router = useRouter();
  const { t } = useTranslation();

  const navigation = [
    { 
      name: t('nav.home'), 
      href: `/${locale}` 
    },
    { 
      name: t('nav.aboutUs'), 
      href: `/${locale}/about-us` 
    },
    {
      name: t('nav.services'),
      href: `/${locale}/services`,
      dropdown: [
        { name: t('services.judicialDisputes'), href: `/${locale}/services/judicial-disputes` },
        { name: t('services.corporateAndInvestment'), href: `/${locale}/services/corporate-and-investment` },
        { name: t('services.arbitrationAndDispute'), href: `/${locale}/services/arbitration-and-dispute-settlement` },
        { name: t('services.legalAdvice'), href: `/${locale}/services/legal-advice` },
        { name: t('services.propertySector'), href: `/${locale}/services/property-sector` },
        { name: t('services.foreignersAndInvestors'), href: `/${locale}/services/foreigners-and-investors-affairs` },
        { name: t('services.governmentRegulations'), href: `/${locale}/services/government-regulations-and-licensing` },
        { name: t('services.contractsSector'), href: `/${locale}/services/contracts-sector` }
      ]
    },
    {
      name: t('nav.legalResearch'),
      href: `/${locale}/legal-research-and-training`,
      dropdown: [
        { name: t('blogSystems.legalResearch.legalTraining'), href: `/${locale}/legal-research-and-training/legal-and-vocational-training` },
        { name: t('blogSystems.legalResearch.legalResearch'), href: `/${locale}/legal-research-and-training/legal-research` }
      ]
    },
    {
      name: t('nav.treasuresOfLaw'),
      href: `/${locale}/treasures-of-law`,
      dropdown: [
        { name: t('blogSystems.treasuresOfLaw.legalPrinciples'), href: `/${locale}/treasures-of-law/legal-principles` },
        { name: t('blogSystems.treasuresOfLaw.legislations'), href: `/${locale}/treasures-of-law/legislations` },
        { name: t('blogSystems.treasuresOfLaw.legalBooks'), href: `/${locale}/treasures-of-law/legal-books` },
        { name: t('blogSystems.treasuresOfLaw.legalTemplates'), href: `/${locale}/treasures-of-law/legal-templates` },
        { name: t('blogSystems.treasuresOfLaw.contractTemplates'), href: `/${locale}/treasures-of-law/contract-templates` }
      ]
    },
    {
      name: t('nav.blog'),
      href: `/${locale}/blog`,
      dropdown: [
        { name: t('blogSystems.blog.legislativeReleases'), href: `/${locale}/blog/category/legislative-releases` },
        { name: t('blogSystems.blog.updatedLegalPrinciples'), href: `/${locale}/blog/category/updated-legal-principles` },
        { name: t('blogSystems.blog.commonLegalQuestions'), href: `/${locale}/blog/category/common-legal-questions` },
        { name: t('blogSystems.blog.judicialNews'), href: `/${locale}/blog/category/judicial-news` },
        { name: t('blogSystems.blog.latestResolutions'), href: `/${locale}/blog/category/latest-resolutions-and-legislations` }
      ]
    },
    { 
      name: t('nav.ourTeam'), 
      href: `/${locale}/our-team` 
    },
    { 
      name: t('nav.contactUs'), 
      href: `/${locale}/contact-us` 
    }
  ];

  const handleDropdownToggle = (index) => {
    setActiveDropdown(activeDropdown === index ? null : index);
  };

  return (
    <header className="header-container">
      {/* Top Bar */}
      <div className="header-top-bar">
        <div className="container mx-auto px-4">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 text-sm">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-6">
              <div className="flex items-center">
                <FaPhone className="ml-2 text-[#B08D57]" />
                <span>+20 2 1234 5678</span>
              </div>
              <div className="flex items-center">
                <FaEnvelope className="ml-2 text-[#B08D57]" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center">
                <svg className="w-4 h-4 ml-2 text-[#B08D57]" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
                <span>القاهرة، مصر</span>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <LanguageSwitcher currentLocale={locale} />
            </div>
          </div>
        </div>
      </div>

      {/* Main Navigation */}
      <nav className="header-nav">
        <div className="container mx-auto px-4">
          <div className="relative flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
            {/* Logo */}
            <Link href={`/${locale}`} className="flex items-center justify-center lg:justify-start">
              <div className="logo-container">
                <div className="logo-title">
                  مكتب المحاماة
                </div>
                <div className="logo-subtitle">
                  للاستشارات القانونية المتخصصة
                </div>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center gap-6">
              {navigation.map((item, index) => (
                <div key={item.name} className="relative group">
                  {item.dropdown ? (
                    <>
                      <button
                        className={`nav-link flex items-center gap-1 ${
                          router.pathname.startsWith(item.href) ? 'active' : ''
                        }`}
                        onMouseEnter={() => setActiveDropdown(index)}
                      >
                        {item.name}
                        <FaChevronDown className="text-xs" />
                      </button>
                      <div 
                        className="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50"
                        onMouseLeave={() => setActiveDropdown(null)}
                      >
                        <div className="py-2">
                          {item.dropdown.map((subItem) => (
                            <Link
                              key={subItem.name}
                              href={subItem.href}
                              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors"
                            >
                              {subItem.name}
                            </Link>
                          ))}
                        </div>
                      </div>
                    </>
                  ) : (
                    <Link
                      href={item.href}
                      className={`nav-link ${
                        router.pathname === item.href ? 'active' : ''
                      }`}
                    >
                      {item.name}
                    </Link>
                  )}
                </div>
              ))}
              <Link href={`/${locale}/contact-us`}>
                <button className="btn-consultation">
                  {t('nav.freeConsultation')}
                </button>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              {isMenuOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
            </button>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="lg:hidden mt-6 pb-4 border-t border-gray-200 pt-4">
              <div className="flex flex-col gap-2">
                {navigation.map((item, index) => (
                  <div key={item.name}>
                    {item.dropdown ? (
                      <>
                        <button
                          onClick={() => handleDropdownToggle(index)}
                          className="nav-link w-full text-left flex items-center justify-between"
                        >
                          {item.name}
                          <FaChevronDown className={`text-xs transition-transform ${
                            activeDropdown === index ? 'rotate-180' : ''
                          }`} />
                        </button>
                        {activeDropdown === index && (
                          <div className="ml-4 mt-2 space-y-2">
                            {item.dropdown.map((subItem) => (
                              <Link
                                key={subItem.name}
                                href={subItem.href}
                                className="block text-sm text-gray-600 hover:text-primary-600 transition-colors py-1"
                                onClick={() => setIsMenuOpen(false)}
                              >
                                {subItem.name}
                              </Link>
                            ))}
                          </div>
                        )}
                      </>
                    ) : (
                      <Link
                        href={item.href}
                        className={`nav-link ${
                          router.pathname === item.href ? 'active' : ''
                        }`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {item.name}
                      </Link>
                    )}
                  </div>
                ))}
                <Link href={`/${locale}/contact-us`}>
                  <button className="btn-consultation mt-4 w-full" onClick={() => setIsMenuOpen(false)}>
                    {t('nav.freeConsultation')}
                  </button>
                </Link>
              </div>
            </div>
          )}
        </div>
      </nav>
    </header>
  );
};

export default LocalizedHeader;
