import { useState, useEffect } from 'react';
import {
  FaChartLine,
  FaClock,
  FaServer,
  FaEye,
  FaUsers,
  FaArrowUp,
  FaArrowDown
} from 'react-icons/fa';

const PerformanceStats = () => {
  const [stats] = useState({
    pageViews: 1250,
    uniqueVisitors: 890,
    avgLoadTime: 1.2,
    serverUptime: 99.9
  });

  useEffect(() => {
    // مكون مبسط للإحصائيات الأساسية
  }, []);

  const StatCard = ({ title, value, icon: Icon, color, change, changeType }) => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-2">{value}</p>
          {change && (
            <div className={`flex items-center mt-2 text-sm ${
              changeType === 'increase' ? 'text-green-600' : 'text-red-600'
            }`}>
              {changeType === 'increase' ? <FaArrowUp className="ml-1" /> : <FaArrowDown className="ml-1" />}
              {change}% من الأسبوع الماضي
            </div>
          )}
        </div>
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${color}`}>
          <Icon className="text-2xl text-white" />
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-bold text-gray-900 mb-2 flex items-center">
            <FaChartLine className="text-primary-600 ml-3" />
            إحصائيات الأداء
          </h2>
          <p className="text-gray-600">نظرة عامة على أداء الموقع</p>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="مشاهدات الصفحة"
          value={stats.pageViews.toLocaleString()}
          icon={FaEye}
          color="bg-blue-500"
          change={12}
          changeType="increase"
        />
        <StatCard
          title="الزوار الفريدون"
          value={stats.uniqueVisitors.toLocaleString()}
          icon={FaUsers}
          color="bg-green-500"
          change={8}
          changeType="increase"
        />
        <StatCard
          title="متوسط التحميل"
          value={`${stats.avgLoadTime}s`}
          icon={FaClock}
          color="bg-yellow-500"
          change={5}
          changeType="decrease"
        />
        <StatCard
          title="وقت التشغيل"
          value={`${stats.serverUptime}%`}
          icon={FaServer}
          color="bg-purple-500"
          change={0.1}
          changeType="increase"
        />
      </div>
    </div>
  );
};

export default PerformanceStats;
