import SimpleAdminLayout from '../../components/admin/SimpleAdminLayout';
import { 
  FaUsers, 
  FaFileAlt, 
  FaNewspaper, 
  FaConciergeBell,
  FaChartLine,
  FaEye,
  FaCalendarAlt,
  FaLaptop
} from 'react-icons/fa';

export default function SimpleAdminDashboard() {
  const stats = [
    {
      name: 'إجمالي المقالات',
      value: '24',
      icon: FaFileAlt,
      color: 'bg-blue-500',
      change: '+12%',
      changeType: 'increase'
    },
    {
      name: 'الأخبار',
      value: '18',
      icon: FaNewspaper,
      color: 'bg-green-500',
      change: '+8%',
      changeType: 'increase'
    },
    {
      name: 'الخدمات',
      value: '12',
      icon: FaConciergeBell,
      color: 'bg-purple-500',
      change: '+5%',
      changeType: 'increase'
    },
    {
      name: 'الخدمات الإلكترونية',
      value: '8',
      icon: FaLaptop,
      color: 'bg-orange-500',
      change: '+15%',
      changeType: 'increase'
    }
  ];

  const recentActivities = [
    {
      id: 1,
      action: 'تم إضافة مقال جديد',
      title: 'قانون العمل الجديد',
      time: 'منذ ساعتين',
      type: 'article'
    },
    {
      id: 2,
      action: 'تم تحديث خدمة',
      title: 'الاستشارات القانونية',
      time: 'منذ 4 ساعات',
      type: 'service'
    },
    {
      id: 3,
      action: 'تم نشر خبر جديد',
      title: 'تحديثات القانون التجاري',
      time: 'منذ يوم واحد',
      type: 'news'
    }
  ];

  return (
    <SimpleAdminLayout title="لوحة التحكم الرئيسية">
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg shadow-lg p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">مرحباً بك في لوحة التحكم</h1>
          <p className="text-primary-100">إدارة محتوى موقع مكتب المحاماة</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat) => (
            <div key={stat.name} className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className={`${stat.color} rounded-lg p-3`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
              </div>
              <div className="mt-4">
                <span className={`text-sm font-medium ${
                  stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.change}
                </span>
                <span className="text-sm text-gray-500 mr-2">من الشهر الماضي</span>
              </div>
            </div>
          ))}
        </div>

        {/* Recent Activities */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">الأنشطة الأخيرة</h3>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                      {activity.type === 'article' && <FaFileAlt className="h-4 w-4 text-primary-600" />}
                      {activity.type === 'service' && <FaConciergeBell className="h-4 w-4 text-primary-600" />}
                      {activity.type === 'news' && <FaNewspaper className="h-4 w-4 text-primary-600" />}
                    </div>
                  </div>
                  <div className="mr-3 flex-1">
                    <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                    <p className="text-sm text-gray-600">{activity.title}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h3>
            <div className="space-y-3">
              <a
                href="/admin/articles/new"
                className="block w-full bg-primary-600 text-white text-center py-3 px-4 rounded-lg hover:bg-primary-700 transition-colors"
              >
                <FaFileAlt className="inline ml-2" />
                إضافة مقال جديد
              </a>
              <a
                href="/admin/news/new"
                className="block w-full bg-green-600 text-white text-center py-3 px-4 rounded-lg hover:bg-green-700 transition-colors"
              >
                <FaNewspaper className="inline ml-2" />
                إضافة خبر جديد
              </a>
              <a
                href="/admin/services/new"
                className="block w-full bg-purple-600 text-white text-center py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors"
              >
                <FaConciergeBell className="inline ml-2" />
                إضافة خدمة جديدة
              </a>
              <a
                href="/admin/electronic-services/new"
                className="block w-full bg-orange-600 text-white text-center py-3 px-4 rounded-lg hover:bg-orange-700 transition-colors"
              >
                <FaLaptop className="inline ml-2" />
                إضافة خدمة إلكترونية
              </a>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">حالة النظام</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
              <p className="text-sm font-medium text-gray-900">الخادم</p>
              <p className="text-xs text-green-600">يعمل بشكل طبيعي</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
              <p className="text-sm font-medium text-gray-900">قاعدة البيانات</p>
              <p className="text-xs text-green-600">متصلة</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
              <p className="text-sm font-medium text-gray-900">النسخ الاحتياطي</p>
              <p className="text-xs text-green-600">محدث</p>
            </div>
          </div>
        </div>
      </div>
    </SimpleAdminLayout>
  );
}
