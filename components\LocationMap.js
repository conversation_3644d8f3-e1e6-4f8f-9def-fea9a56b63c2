import { useState } from 'react';
import { FaMapMarkerAlt, FaDirections, FaCopy, FaExternalLinkAlt } from 'react-icons/fa';

const LocationMap = () => {
  const [copied, setCopied] = useState(false);
  
  // إحداثيات الموقع
  const coordinates = {
    lat: 30.038704,
    lng: 31.343061,
    address: "30°02'19.3\"N 31°20'35.0\"E"
  };

  // رابط خرائط جوجل
  const googleMapsUrl = `https://www.google.com/maps?ll=${coordinates.lat},${coordinates.lng}&z=16&t=m&hl=ar&gl=EG&mapclient=embed&q=${coordinates.lat},${coordinates.lng}`;
  
  // رابط الاتجاهات
  const directionsUrl = `https://www.google.com/maps/dir//${coordinates.lat},${coordinates.lng}/@${coordinates.lat},${coordinates.lng},16z`;

  // نسخ الإحداثيات
  const copyCoordinates = async () => {
    try {
      await navigator.clipboard.writeText(`${coordinates.lat}, ${coordinates.lng}`);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('فشل في نسخ الإحداثيات:', err);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-[#0A2A4E] to-[#1A3A5E] text-white p-6">
        <div className="flex items-center mb-4">
          <FaMapMarkerAlt className="text-[#B08D57] text-2xl ml-3" />
          <h3 className="text-xl font-bold">موقع المكتب</h3>
        </div>
        <p className="text-gray-200">
          مكتب المحاماة للاستشارات القانونية المتخصصة
        </p>
      </div>

      {/* Map Container */}
      <div className="relative">
        <iframe
          src={`https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3454.123456789!2d${coordinates.lng}!3d${coordinates.lat}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzDCsDAyJzE5LjMiTiAzMcKwMjAnMzUuMCJF!5e0!3m2!1sar!2seg!4v1234567890123!5m2!1sar!2seg`}
          width="100%"
          height="300"
          style={{ border: 0 }}
          allowFullScreen=""
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
          className="w-full"
        ></iframe>
        
        {/* Overlay with location info */}
        <div className="absolute top-4 right-4 bg-white/95 backdrop-blur-sm rounded-lg p-3 shadow-lg">
          <div className="text-sm text-gray-600 mb-1">الإحداثيات</div>
          <div className="font-mono text-xs text-gray-800">{coordinates.address}</div>
        </div>
      </div>

      {/* Actions */}
      <div className="p-6 bg-gray-50">
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          {/* فتح في خرائط جوجل */}
          <a
            href={googleMapsUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center bg-[#B08D57] text-white px-4 py-3 rounded-lg hover:bg-[#9A7A4A] transition-colors duration-200 font-medium"
          >
            <FaExternalLinkAlt className="ml-2" />
            فتح في الخرائط
          </a>

          {/* الحصول على الاتجاهات */}
          <a
            href={directionsUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center bg-[#0A2A4E] text-white px-4 py-3 rounded-lg hover:bg-[#083A5E] transition-colors duration-200 font-medium"
          >
            <FaDirections className="ml-2" />
            الاتجاهات
          </a>

          {/* نسخ الإحداثيات */}
          <button
            onClick={copyCoordinates}
            className={`flex items-center justify-center px-4 py-3 rounded-lg transition-colors duration-200 font-medium ${
              copied 
                ? 'bg-green-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            <FaCopy className="ml-2" />
            {copied ? 'تم النسخ!' : 'نسخ الإحداثيات'}
          </button>
        </div>

        {/* معلومات إضافية */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">معلومات الموقع</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• القاهرة، جمهورية مصر العربية</li>
                <li>• منطقة مركزية ومتميزة</li>
                <li>• سهولة الوصول بالمواصلات</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">ساعات العمل</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• السبت - الخميس: 9:00 ص - 6:00 م</li>
                <li>• الجمعة: مغلق</li>
                <li>• استشارات طارئة: 24/7</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LocationMap;
