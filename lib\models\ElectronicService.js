import mongoose from 'mongoose';

const electronicServiceSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'عنوان الخدمة الإلكترونية مطلوب'],
    trim: true,
    maxlength: [200, 'العنوان يجب أن يكون أقل من 200 حرف']
  },
  slug: {
    type: String,
    required: [true, 'الرابط المختصر مطلوب'],
    trim: true,
    lowercase: true,
    match: [/^[a-z0-9-]+$/, 'الرابط المختصر يجب أن يحتوي على أحرف إنجليزية وأرقام وشرطات فقط']
  },
  excerpt: {
    type: String,
    required: [true, 'المقتطف مطلوب'],
    trim: true,
    maxlength: [500, 'المقتطف يجب أن يكون أقل من 500 حرف']
  },
  content: {
    type: String,
    required: [true, 'محتوى الخدمة مطلوب']
  },
  image: {
    type: String,
    trim: true
  },
  category: {
    type: String,
    required: [true, 'فئة الخدمة مطلوبة'],
    trim: true
  },
  tags: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived', 'maintenance'],
    default: 'draft'
  },
  featured: {
    type: Boolean,
    default: false
  },
  price: {
    type: String,
    trim: true
  },
  duration: {
    type: String,
    trim: true
  },
  requirements: {
    type: String,
    trim: true
  },
  steps: [{
    order: {
      type: Number,
      required: true
    },
    title: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    isRequired: {
      type: Boolean,
      default: true
    }
  }],
  documents: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    isRequired: {
      type: Boolean,
      default: true
    },
    format: {
      type: String,
      enum: ['pdf', 'image', 'any'],
      default: 'any'
    },
    maxSize: {
      type: Number,
      default: 5242880 // 5MB
    }
  }],
  forms: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    url: {
      type: String,
      trim: true
    },
    isDownloadable: {
      type: Boolean,
      default: true
    }
  }],
  fees: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    currency: {
      type: String,
      default: 'SAR'
    },
    isOptional: {
      type: Boolean,
      default: false
    }
  }],
  deliveryMethods: [{
    type: String,
    enum: ['email', 'sms', 'portal', 'pickup', 'mail'],
    default: 'email'
  }],
  supportedLanguages: [{
    type: String,
    enum: ['ar', 'en'],
    default: 'ar'
  }],
  availability: {
    isAvailable: {
      type: Boolean,
      default: true
    },
    maintenanceMessage: {
      type: String,
      trim: true
    },
    scheduledMaintenance: {
      start: Date,
      end: Date,
      message: String
    }
  },
  statistics: {
    views: {
      type: Number,
      default: 0
    },
    applications: {
      type: Number,
      default: 0
    },
    completions: {
      type: Number,
      default: 0
    },
    averageRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    ratingsCount: {
      type: Number,
      default: 0
    }
  },
  meta: {
    title: {
      type: String,
      trim: true,
      maxlength: [60, 'عنوان SEO يجب أن يكون أقل من 60 حرف']
    },
    description: {
      type: String,
      trim: true,
      maxlength: [160, 'وصف SEO يجب أن يكون أقل من 160 حرف']
    },
    keywords: {
      type: String,
      trim: true
    }
  }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
});

// فهرسة للبحث والأداء
electronicServiceSchema.index({ slug: 1 }, { unique: true });
electronicServiceSchema.index({ status: 1 });
electronicServiceSchema.index({ category: 1 });
electronicServiceSchema.index({ featured: 1 });
electronicServiceSchema.index({ 'availability.isAvailable': 1 });
electronicServiceSchema.index({ created_at: -1 });
electronicServiceSchema.index({ 
  title: 'text', 
  excerpt: 'text', 
  content: 'text',
  tags: 'text'
}, {
  weights: {
    title: 10,
    excerpt: 5,
    content: 1,
    tags: 3
  }
});

// Virtual للتحقق من الصيانة المجدولة
electronicServiceSchema.virtual('isUnderMaintenance').get(function() {
  if (!this.availability.isAvailable) return true;
  
  const now = new Date();
  const maintenance = this.availability.scheduledMaintenance;
  
  if (maintenance && maintenance.start && maintenance.end) {
    return now >= maintenance.start && now <= maintenance.end;
  }
  
  return false;
});

// Virtual لحساب معدل الإكمال
electronicServiceSchema.virtual('completionRate').get(function() {
  if (this.statistics.applications === 0) return 0;
  return (this.statistics.completions / this.statistics.applications) * 100;
});

// طرق النموذج
electronicServiceSchema.methods.incrementViews = function() {
  return this.updateOne({ $inc: { 'statistics.views': 1 } });
};

electronicServiceSchema.methods.incrementApplications = function() {
  return this.updateOne({ $inc: { 'statistics.applications': 1 } });
};

electronicServiceSchema.methods.incrementCompletions = function() {
  return this.updateOne({ $inc: { 'statistics.completions': 1 } });
};

electronicServiceSchema.methods.addRating = function(rating) {
  const currentAverage = this.statistics.averageRating;
  const currentCount = this.statistics.ratingsCount;
  
  const newCount = currentCount + 1;
  const newAverage = ((currentAverage * currentCount) + rating) / newCount;
  
  return this.updateOne({
    'statistics.averageRating': Math.round(newAverage * 10) / 10,
    'statistics.ratingsCount': newCount
  });
};

electronicServiceSchema.methods.setMaintenance = function(message) {
  return this.updateOne({
    'availability.isAvailable': false,
    'availability.maintenanceMessage': message
  });
};

electronicServiceSchema.methods.scheduleMaintenance = function(start, end, message) {
  return this.updateOne({
    'availability.scheduledMaintenance': { start, end, message }
  });
};

electronicServiceSchema.methods.endMaintenance = function() {
  return this.updateOne({
    'availability.isAvailable': true,
    'availability.maintenanceMessage': null,
    'availability.scheduledMaintenance': {}
  });
};

// طرق ثابتة
electronicServiceSchema.statics.findPublished = function() {
  return this.find({ status: 'published' }).sort({ created_at: -1 });
};

electronicServiceSchema.statics.findAvailable = function() {
  return this.find({ 
    status: 'published',
    'availability.isAvailable': true
  }).sort({ created_at: -1 });
};

electronicServiceSchema.statics.findFeatured = function() {
  return this.find({ 
    status: 'published', 
    featured: true,
    'availability.isAvailable': true
  }).sort({ created_at: -1 });
};

electronicServiceSchema.statics.findByCategory = function(category) {
  return this.find({ 
    status: 'published', 
    category,
    'availability.isAvailable': true
  }).sort({ created_at: -1 });
};

electronicServiceSchema.statics.searchServices = function(query) {
  return this.find(
    { 
      $text: { $search: query },
      status: 'published',
      'availability.isAvailable': true
    },
    { score: { $meta: 'textScore' } }
  ).sort({ score: { $meta: 'textScore' } });
};

electronicServiceSchema.statics.getPopular = function(limit = 10) {
  return this.find({ 
    status: 'published',
    'availability.isAvailable': true
  })
    .sort({ 'statistics.views': -1, 'statistics.applications': -1 })
    .limit(limit);
};

// تصدير النموذج
const ElectronicService = mongoose.models.ElectronicService || mongoose.model('ElectronicService', electronicServiceSchema);

export default ElectronicService;
