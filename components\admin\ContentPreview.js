import { useState } from 'react';
import { FaEye, FaCode, FaMobile, FaTablet, FaDesktop, FaPrint } from 'react-icons/fa';

const ContentPreview = ({ content, title, excerpt, className = '' }) => {
  const [viewMode, setViewMode] = useState('desktop'); // desktop, tablet, mobile
  const [showCode, setShowCode] = useState(false);

  const viewModes = {
    desktop: { icon: FaDesktop, label: 'سطح المكتب', width: '100%' },
    tablet: { icon: FaTablet, label: 'جهاز لوحي', width: '768px' },
    mobile: { icon: FaMobile, label: 'هاتف محمول', width: '375px' }
  };

  // تنظيف HTML وإضافة أنماط للمعاينة
  const getCleanHTML = () => {
    return content
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // إزالة JavaScript
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '') // إزالة iframes
      .replace(/on\w+="[^"]*"/g, ''); // إزالة event handlers
  };

  const previewStyles = `
    .content-preview {
      font-family: 'Cairo', 'Tajawal', sans-serif;
      direction: rtl;
      text-align: right;
      line-height: 1.8;
      color: #1f2937;
    }
    
    .content-preview h1 {
      font-size: 2.5rem;
      font-weight: bold;
      margin: 1.5rem 0;
      color: #111827;
      border-bottom: 3px solid #3b82f6;
      padding-bottom: 0.5rem;
    }
    
    .content-preview h2 {
      font-size: 2rem;
      font-weight: bold;
      margin: 1.25rem 0;
      color: #1f2937;
    }
    
    .content-preview h3 {
      font-size: 1.75rem;
      font-weight: bold;
      margin: 1rem 0;
      color: #374151;
    }
    
    .content-preview h4 {
      font-size: 1.5rem;
      font-weight: bold;
      margin: 0.875rem 0;
      color: #4b5563;
    }
    
    .content-preview h5 {
      font-size: 1.25rem;
      font-weight: bold;
      margin: 0.75rem 0;
      color: #6b7280;
    }
    
    .content-preview h6 {
      font-size: 1.125rem;
      font-weight: bold;
      margin: 0.625rem 0;
      color: #9ca3af;
    }
    
    .content-preview p {
      margin: 1rem 0;
      text-align: justify;
    }
    
    .content-preview blockquote {
      border-right: 4px solid #3b82f6;
      background: #f8fafc;
      padding: 1rem 1.5rem;
      margin: 1.5rem 0;
      font-style: italic;
      color: #64748b;
      border-radius: 0.5rem;
    }
    
    .content-preview ul, .content-preview ol {
      margin: 1rem 0;
      padding-right: 2rem;
    }
    
    .content-preview li {
      margin: 0.5rem 0;
    }
    
    .content-preview a {
      color: #3b82f6;
      text-decoration: underline;
      transition: color 0.2s;
    }
    
    .content-preview a:hover {
      color: #1d4ed8;
    }
    
    .content-preview img {
      max-width: 100%;
      height: auto;
      border-radius: 0.75rem;
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
      margin: 1.5rem auto;
      display: block;
    }
    
    .content-preview video {
      max-width: 100%;
      height: auto;
      border-radius: 0.75rem;
      margin: 1.5rem auto;
      display: block;
    }
    
    .content-preview code {
      background: #f1f5f9;
      color: #e11d48;
      padding: 0.25rem 0.5rem;
      border-radius: 0.375rem;
      font-family: 'Courier New', monospace;
      font-size: 0.875rem;
    }
    
    .content-preview pre {
      background: #1e293b;
      color: #f8fafc;
      padding: 1.5rem;
      border-radius: 0.75rem;
      overflow-x: auto;
      margin: 1.5rem 0;
      direction: ltr;
      text-align: left;
    }
    
    .content-preview pre code {
      background: transparent;
      color: inherit;
      padding: 0;
    }
    
    .content-preview table {
      width: 100%;
      border-collapse: collapse;
      margin: 1.5rem 0;
      background: white;
      border-radius: 0.5rem;
      overflow: hidden;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .content-preview th, .content-preview td {
      padding: 0.75rem 1rem;
      text-align: right;
      border-bottom: 1px solid #e5e7eb;
    }
    
    .content-preview th {
      background: #f9fafb;
      font-weight: bold;
      color: #374151;
    }
    
    .content-preview tr:hover {
      background: #f9fafb;
    }
    
    @media print {
      .content-preview {
        font-size: 12pt;
        line-height: 1.6;
      }
      
      .content-preview h1 {
        font-size: 18pt;
      }
      
      .content-preview h2 {
        font-size: 16pt;
      }
      
      .content-preview h3 {
        font-size: 14pt;
      }
    }
  `;

  return (
    <div className={`bg-white rounded-xl shadow-sm border border-gray-200 ${className}`}>
      {/* شريط التحكم */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <FaEye className="text-gray-500" />
          <span className="font-medium text-gray-700">معاينة المحتوى</span>
        </div>
        
        <div className="flex items-center gap-2">
          {/* أزرار حجم الشاشة */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            {Object.entries(viewModes).map(([mode, config]) => (
              <button
                key={mode}
                onClick={() => setViewMode(mode)}
                className={`p-2 rounded transition-colors duration-200 ${
                  viewMode === mode 
                    ? 'bg-white text-primary-600 shadow-sm' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title={config.label}
              >
                <config.icon />
              </button>
            ))}
          </div>
          
          {/* زر عرض الكود */}
          <button
            onClick={() => setShowCode(!showCode)}
            className={`p-2 rounded transition-colors duration-200 ${
              showCode 
                ? 'bg-gray-200 text-gray-700' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            title="عرض الكود"
          >
            <FaCode />
          </button>
          
          {/* زر الطباعة */}
          <button
            onClick={() => window.print()}
            className="p-2 text-gray-500 hover:text-gray-700 rounded transition-colors duration-200"
            title="طباعة"
          >
            <FaPrint />
          </button>
        </div>
      </div>

      {/* منطقة المعاينة */}
      <div className="p-6">
        <div 
          className="mx-auto transition-all duration-300"
          style={{ 
            maxWidth: viewModes[viewMode].width,
            border: viewMode !== 'desktop' ? '1px solid #e5e7eb' : 'none',
            borderRadius: viewMode !== 'desktop' ? '0.75rem' : '0',
            padding: viewMode !== 'desktop' ? '1rem' : '0'
          }}
        >
          {showCode ? (
            // عرض الكود
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">HTML Code</h3>
                <pre className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm">
                  <code>{getCleanHTML()}</code>
                </pre>
              </div>
            </div>
          ) : (
            // عرض المحتوى المنسق
            <div>
              {/* العنوان والملخص */}
              {title && (
                <div className="mb-6 pb-6 border-b border-gray-200">
                  <h1 className="text-3xl font-bold text-gray-900 mb-4">{title}</h1>
                  {excerpt && (
                    <p className="text-lg text-gray-600 leading-relaxed">{excerpt}</p>
                  )}
                </div>
              )}
              
              {/* المحتوى */}
              <div 
                className="content-preview prose prose-lg max-w-none"
                dangerouslySetInnerHTML={{ __html: getCleanHTML() }}
              />
            </div>
          )}
        </div>
      </div>

      {/* إضافة الأنماط */}
      <style jsx>{previewStyles}</style>
    </div>
  );
};

export default ContentPreview;
