import { useState } from 'react';
import { FaDownload, FaEye, FaFilePdf, FaTimes } from 'react-icons/fa';

const PDFViewer = ({ url, name, size }) => {
  const [showPreview, setShowPreview] = useState(false);

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = url;
    link.download = name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 بايت';
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <>
      <div className="pdf-attachment">
        <div className="pdf-header">
          <div className="pdf-icon">
            <FaFilePdf className="text-red-600 text-2xl" />
          </div>
          <div className="pdf-info">
            <h4 className="pdf-title">{name}</h4>
            <p className="pdf-size">حجم الملف: {formatFileSize(size)}</p>
          </div>
        </div>
        <div className="pdf-actions">
          <button
            onClick={() => setShowPreview(true)}
            className="pdf-btn pdf-btn-view"
          >
            <FaEye />
            <span>عرض الملف</span>
          </button>
          <button
            onClick={handleDownload}
            className="pdf-btn pdf-btn-download"
          >
            <FaDownload />
            <span>تحميل الملف</span>
          </button>
        </div>
      </div>

      {/* نافذة المعاينة */}
      {showPreview && (
        <div className="pdf-preview-overlay">
          <div className="pdf-preview-modal">
            <div className="pdf-preview-header">
              <h3 className="pdf-preview-title">{name}</h3>
              <button
                onClick={() => setShowPreview(false)}
                className="pdf-preview-close"
              >
                <FaTimes />
              </button>
            </div>
            <div className="pdf-preview-content">
              <iframe
                src={url}
                className="pdf-iframe"
                title={name}
              />
            </div>
            <div className="pdf-preview-footer">
              <button
                onClick={handleDownload}
                className="pdf-btn pdf-btn-download"
              >
                <FaDownload />
                <span>تحميل الملف</span>
              </button>
              <button
                onClick={() => setShowPreview(false)}
                className="pdf-btn pdf-btn-secondary"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        .pdf-attachment {
          border: 2px solid #e5e7eb;
          border-radius: 12px;
          padding: 20px;
          margin: 20px 0;
          background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
          direction: rtl;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
        }

        .pdf-attachment:hover {
          border-color: #3b82f6;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .pdf-header {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 16px;
        }

        .pdf-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          background-color: #fee2e2;
          border-radius: 12px;
        }

        .pdf-info {
          flex: 1;
        }

        .pdf-title {
          font-size: 16px;
          font-weight: 600;
          color: #374151;
          margin: 0 0 4px 0;
          line-height: 1.4;
        }

        .pdf-size {
          font-size: 14px;
          color: #6b7280;
          margin: 0;
        }

        .pdf-actions {
          display: flex;
          gap: 12px;
          flex-wrap: wrap;
        }

        .pdf-btn {
          display: inline-flex;
          align-items: center;
          gap: 8px;
          padding: 10px 16px;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
          text-decoration: none;
          border: none;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .pdf-btn-view {
          background-color: #3b82f6;
          color: white;
        }

        .pdf-btn-view:hover {
          background-color: #2563eb;
          transform: translateY(-1px);
        }

        .pdf-btn-download {
          background-color: #10b981;
          color: white;
        }

        .pdf-btn-download:hover {
          background-color: #059669;
          transform: translateY(-1px);
        }

        .pdf-btn-secondary {
          background-color: #6b7280;
          color: white;
        }

        .pdf-btn-secondary:hover {
          background-color: #4b5563;
        }

        .pdf-preview-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.75);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
          padding: 20px;
        }

        .pdf-preview-modal {
          background: white;
          border-radius: 12px;
          width: 100%;
          max-width: 900px;
          max-height: 90vh;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .pdf-preview-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px;
          border-bottom: 1px solid #e5e7eb;
          background-color: #f9fafb;
        }

        .pdf-preview-title {
          font-size: 18px;
          font-weight: 600;
          color: #374151;
          margin: 0;
        }

        .pdf-preview-close {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border: none;
          background-color: #f3f4f6;
          border-radius: 8px;
          cursor: pointer;
          color: #6b7280;
          transition: all 0.2s ease;
        }

        .pdf-preview-close:hover {
          background-color: #e5e7eb;
          color: #374151;
        }

        .pdf-preview-content {
          flex: 1;
          padding: 20px;
          background-color: #f9fafb;
        }

        .pdf-iframe {
          width: 100%;
          height: 500px;
          border: none;
          border-radius: 8px;
          background: white;
        }

        .pdf-preview-footer {
          display: flex;
          justify-content: flex-end;
          gap: 12px;
          padding: 20px;
          border-top: 1px solid #e5e7eb;
          background-color: #f9fafb;
        }

        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 768px) {
          .pdf-attachment {
            padding: 16px;
            margin: 16px 0;
          }

          .pdf-header {
            gap: 12px;
            margin-bottom: 12px;
          }

          .pdf-icon {
            width: 40px;
            height: 40px;
          }

          .pdf-title {
            font-size: 14px;
          }

          .pdf-size {
            font-size: 12px;
          }

          .pdf-actions {
            gap: 8px;
          }

          .pdf-btn {
            padding: 8px 12px;
            font-size: 12px;
          }

          .pdf-preview-modal {
            margin: 10px;
            max-height: 95vh;
          }

          .pdf-preview-header,
          .pdf-preview-footer {
            padding: 16px;
          }

          .pdf-preview-content {
            padding: 16px;
          }

          .pdf-iframe {
            height: 400px;
          }

          .pdf-preview-footer {
            flex-direction: column;
          }
        }
      `}</style>
    </>
  );
};

export default PDFViewer;
